{"__meta": {"id": "X8fc89d2a1945dea9964781b5e13c8f0a", "datetime": "2025-06-19 12:33:50", "utime": **********.804216, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750336429.883842, "end": **********.804241, "duration": 0.9203989505767822, "duration_str": "920ms", "measures": [{"label": "Booting", "start": 1750336429.883842, "relative_start": 0, "end": **********.692867, "relative_end": **********.692867, "duration": 0.8090250492095947, "duration_str": "809ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.692885, "relative_start": 0.8090429306030273, "end": **********.804243, "relative_end": 2.1457672119140625e-06, "duration": 0.1113581657409668, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44838128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02423, "accumulated_duration_str": "24.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7515981, "duration": 0.02332, "duration_str": "23.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.244}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.781472, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 96.244, "width_percent": 3.756}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2130542565 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2130542565\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1971511010 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1971511010\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336427618%7C3%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikc1cFFvT1NNYWg1aVhaOHlEbnFqNFE9PSIsInZhbHVlIjoiNEpuQS91U2t2UzBhMUkzank1cFppWUpLZjJoeHVFNGxwLzhtQlJGOERkN0lsQU9DbEF1cHB0YitYeEVMbitFcGtoQms0RHFOcVFQY01wbWdld1hxSHBoaVRlQWd3cjYrd2FPaktJclJoZW16ZUlySjRRSG4xMkt0T3lTT252ZnNuSFBkcjArR0FYWW5DYnh3T2NUano0b09jTWZVTzJoTXVudmk0YnJUd1MxZmx2ZWI2VFdkL2ROTVJhUU1qMU1VRy9PY0tDaUhBVU90VzdHcGxRdU0xem5lc1B0RTI5NUVFK1VCN2ZWTWg0TERla2tFSlJtYnVyMyt3dm1sZFlWRUNoMjhjK256eld1QVJCYlIybjltL2FjOTJXOFRoNnNxL25mcEFtREpsZWdiVVRJa3piVnNjWjVhTFV2R2Z1R3kwVXdGSVNkRHpTWjdHN1VQUzBEUEQ5VlFqT3B3UE5vNU5lb2dMbzBHVHZkRlhjcDM2dTVLMEhIclRxWmlhdm5NeWVoQ09ZLzJWWlJURTFrUUNYNXpKTzM3MFp5TEVQTUVOb2FRckNFbmdqUFlGNFBsSUg1UHdFeS8waFlRUi9EcTBrb0ExTmlRNkZ1blZhUUk5c0xRZXpEL1FoK2JsZGYxbmVyRU0wSFJsN3BQSVVUM2lTYW51SVkwNlZsKzhCUDgiLCJtYWMiOiJiOTA5ZmQ5ODNjMzIzNDFiMTI2MzQ4ZmI0ZGJiYzMzMmRlMTgxMjFmMTQ0YmNkNWU0OWFjOTNjYjU4NGE0NDFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFiSW1KOVFvcWFQNGhLTDdndU53Vnc9PSIsInZhbHVlIjoiell3T1JkczllM2N5blNsZXhLSjNRTnJpcndQaW04ZG9GZmxkeldGT3owTlR3bTdONHR3d1lIOUxWeGF3V21TUHIwMml0WG9Sb1JVanF4TkV4STlnK2hVN29ia2x5VlNrZ2Rua204aGN2YVRTeWFhc09WWkxKNFZpRkpGd0V1elBJa2JSa2hFa1puTGR4ZzRzTU1oQjdDMEIrMnBIR1dJeWFiVy9XaUlmeU5ZS3dxbUdtUk1wRnpxZVB3cXdVSGlQcUVabVpwRGJFd2Q0VWp2Y25xVzFPbFo1NVArbDFneUpLL1ZBZXFvR0ZUZ0MxemlUZTBHbVZCMEF0SEdZR2NpcVBkdmlYREVtQ2VHNllaSXJYeWJleVlzVFNMSHowRUIzZkt2YVJJTi9jKytKcHkwSjhjR1A5amhhQ2VpaU4xQnM3L21qVHArYklUcWUzZXl3VEMvYXdxZUFSRkR5RHR4TkV6blJTVGJRMGZsREVacEFxWHppb1FKZG1DRXNvY0I1cUp3UHVoOHpOd2xXTDJOeXVXclAyZklnR0E5UGtUOUdyT1JlbGRDeFRBNTVodFJjVEVyeUdjWUs5ZUVqTkZUNmJGMEdVNjh1SkdNTXZFSmpnZFF2MndMbUJObDRoNTkxN1FRWE9WNEtrK20vMWh1b1RvMWtFV3FoOTNURjRNbmkiLCJtYWMiOiJjMTE5NzM3ZmRiOWMxYTZjODBlYzEyMTRiYjI5NmU4ZGUzMDhhYzc2YmE2YzRlOTgxNWRiMGRiZDdjMzllYmVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-725365352 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lYCgmFS8fqkPjKv2BfRzZo9jiALUMXID3HgtHMZN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-725365352\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1790947896 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:33:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InkvQy9ndy9xQU40dkplb3BYMkZHSkE9PSIsInZhbHVlIjoicUIvaVhKV0w5VnZnRnFwWkNyOEQzRVF4QWF1TXZ6aVQrUVVpV0NseTJwdUVJK1kwYTFrQ3oza1dFemVSK2xLaHRHMUNScCtPTEVuYmN1M09tK3FXZGV6dGE4N2tmY2lscldhN3FxSk40WUd4UnZNN3FRVWNEUEJ4MWV6dytUT2FjeEVrMFcvckVEVGRpVHE0MStWWGhqWjlKSEN3TDZDR2kzbmpXdG5RYWlab2ZtOGJEUGdUdzBHQ1JMelRtV0hIQXFoVlhCNUR0cndPZG9la0g2NEMyWTVORkpRcEpZcWFvMHpWWFM5VUtvakJ1UGNHSlNNSFBHZm9NdEppcGJBWXVJeG03WXA1Zyt6SzI0L1E1NDl1eGJqQzVVOG5XU21NVTljZ2NnN1pYNXJBN3hZTzNneW41R1VRZnpJUDZETm96ZElFUkVxNHRZUTM0Uk5MWUxTSVZGR05lSjcwNG9wSis5QTZpLzV5TzU0cEN1VC96MVZLYWVFU0RIZDg0QW1iU1pPVnIvYkw4bnNncnpxbS9ia3gwVkNIb2N3MTVXdm5xUlhUdmRZb0VFMC8waXMzSmpZMCt0dStOVmU5blB2TFJSR3RCN2FFSy9vTlk3KzEzWldTMmNmd2hwOFFGMzlSWGdWSjhRMUhIa1lBcW40eUI5dzF3ZFJ4UzVncFdGeEIiLCJtYWMiOiIzMWEzZTk0YmUxZTg3MGIzZTRjMGFmYTQzZTgzOWZiZmI0OTczYjVhOWYzNjQ5NTkyYWVjMWNkMTMzN2Q4YjgwIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImR5OWVUbSt0QUwyMWIyVEM0N2dnQ1E9PSIsInZhbHVlIjoiK0tDbCtLMTZMVmkwNTlCNzd6YjNlSEZCODFqVVA1Z3RnUDE4NU1OUURJc2ovWUY0U2llVTZOR1NSNkhPWE9WalFIOE5HZEx5YVFlT24wakorUjBQRTJPb1gvZXJWbVE0U3BPS2RldVZkdUN0c3hQSnpHN2RHcHJ3a2d0K0RoMk1JQXA0ZjQ3cEo4dFFvWjEzZjFpMkRkQzExbzZzU1U2Ylpybm44VFVGTXdBeitmbmVSSEVGaWYrbU1oU0doQm5QbVhjQkg0dGdKTlFYa05mRklLOXhBRVFlOTROVFVCU2JQby9XYWlKbHRLWGw4MnNvNXljaE4zZWlyYk5CMDhkQmZjdDRLalptQnlReTdNSUJzYXhoU2VlZFlnejcwTkc5R2wzQm1RQXhNWklQT2hkQ3NaeVRvWVJRMXFLR2g1T1F0RUR0anFHa0p3Vys4dFk5Rm5iRWk0SmNwRHNNMTIxSndOMmpyaThlVS9hN1JTMlpaZHBlcmVhdzNDWTB5MnZUR2lFdFFrR3gwODhFNU91UnVNeTQxRFdSTXdKZVlSbXRZVmRyV3d4U0loeTdTUkM4WEM5QWNSMnpyRTFtTCt1YnVTNENDcVowUFBIcEVZNHJSeVdtZXBEV3NsaTgwV2lnYTErOCtSNHBvcGR2L3h3OVVsZS9JTUNjQ1JRUTc4NEkiLCJtYWMiOiJjMjhhOTlhYWRjZjZjMGIzNzk1MGIwMWYwYTNmNzYxMGM3ZjQzYWZhMDIzMTNiYTZlNTQ2ODQ4YTZlYzQyMjIxIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InkvQy9ndy9xQU40dkplb3BYMkZHSkE9PSIsInZhbHVlIjoicUIvaVhKV0w5VnZnRnFwWkNyOEQzRVF4QWF1TXZ6aVQrUVVpV0NseTJwdUVJK1kwYTFrQ3oza1dFemVSK2xLaHRHMUNScCtPTEVuYmN1M09tK3FXZGV6dGE4N2tmY2lscldhN3FxSk40WUd4UnZNN3FRVWNEUEJ4MWV6dytUT2FjeEVrMFcvckVEVGRpVHE0MStWWGhqWjlKSEN3TDZDR2kzbmpXdG5RYWlab2ZtOGJEUGdUdzBHQ1JMelRtV0hIQXFoVlhCNUR0cndPZG9la0g2NEMyWTVORkpRcEpZcWFvMHpWWFM5VUtvakJ1UGNHSlNNSFBHZm9NdEppcGJBWXVJeG03WXA1Zyt6SzI0L1E1NDl1eGJqQzVVOG5XU21NVTljZ2NnN1pYNXJBN3hZTzNneW41R1VRZnpJUDZETm96ZElFUkVxNHRZUTM0Uk5MWUxTSVZGR05lSjcwNG9wSis5QTZpLzV5TzU0cEN1VC96MVZLYWVFU0RIZDg0QW1iU1pPVnIvYkw4bnNncnpxbS9ia3gwVkNIb2N3MTVXdm5xUlhUdmRZb0VFMC8waXMzSmpZMCt0dStOVmU5blB2TFJSR3RCN2FFSy9vTlk3KzEzWldTMmNmd2hwOFFGMzlSWGdWSjhRMUhIa1lBcW40eUI5dzF3ZFJ4UzVncFdGeEIiLCJtYWMiOiIzMWEzZTk0YmUxZTg3MGIzZTRjMGFmYTQzZTgzOWZiZmI0OTczYjVhOWYzNjQ5NTkyYWVjMWNkMTMzN2Q4YjgwIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImR5OWVUbSt0QUwyMWIyVEM0N2dnQ1E9PSIsInZhbHVlIjoiK0tDbCtLMTZMVmkwNTlCNzd6YjNlSEZCODFqVVA1Z3RnUDE4NU1OUURJc2ovWUY0U2llVTZOR1NSNkhPWE9WalFIOE5HZEx5YVFlT24wakorUjBQRTJPb1gvZXJWbVE0U3BPS2RldVZkdUN0c3hQSnpHN2RHcHJ3a2d0K0RoMk1JQXA0ZjQ3cEo4dFFvWjEzZjFpMkRkQzExbzZzU1U2Ylpybm44VFVGTXdBeitmbmVSSEVGaWYrbU1oU0doQm5QbVhjQkg0dGdKTlFYa05mRklLOXhBRVFlOTROVFVCU2JQby9XYWlKbHRLWGw4MnNvNXljaE4zZWlyYk5CMDhkQmZjdDRLalptQnlReTdNSUJzYXhoU2VlZFlnejcwTkc5R2wzQm1RQXhNWklQT2hkQ3NaeVRvWVJRMXFLR2g1T1F0RUR0anFHa0p3Vys4dFk5Rm5iRWk0SmNwRHNNMTIxSndOMmpyaThlVS9hN1JTMlpaZHBlcmVhdzNDWTB5MnZUR2lFdFFrR3gwODhFNU91UnVNeTQxRFdSTXdKZVlSbXRZVmRyV3d4U0loeTdTUkM4WEM5QWNSMnpyRTFtTCt1YnVTNENDcVowUFBIcEVZNHJSeVdtZXBEV3NsaTgwV2lnYTErOCtSNHBvcGR2L3h3OVVsZS9JTUNjQ1JRUTc4NEkiLCJtYWMiOiJjMjhhOTlhYWRjZjZjMGIzNzk1MGIwMWYwYTNmNzYxMGM3ZjQzYWZhMDIzMTNiYTZlNTQ2ODQ4YTZlYzQyMjIxIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790947896\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1859923056 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859923056\", {\"maxDepth\":0})</script>\n"}}