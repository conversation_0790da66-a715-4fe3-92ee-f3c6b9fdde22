{"__meta": {"id": "Xc553a63843ccd6d62b8a8a6745914346", "datetime": "2025-06-19 12:34:51", "utime": **********.56047, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750336490.583875, "end": **********.560497, "duration": 0.9766221046447754, "duration_str": "977ms", "measures": [{"label": "Booting", "start": 1750336490.583875, "relative_start": 0, "end": **********.451971, "relative_end": **********.451971, "duration": 0.868096113204956, "duration_str": "868ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.451989, "relative_start": 0.8681139945983887, "end": **********.5605, "relative_end": 2.86102294921875e-06, "duration": 0.10851097106933594, "duration_str": "109ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46093376, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013770000000000001, "accumulated_duration_str": "13.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.506609, "duration": 0.0119, "duration_str": "11.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.42}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.535159, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.42, "width_percent": 6.028}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.546979, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.447, "width_percent": 7.553}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-287656050 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-287656050\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1443845080 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1443845080\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2126550663 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126550663\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-228545336 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336482500%7C5%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ino0OU9yRjVPSmM4UU5pOUZ2NUVmK2c9PSIsInZhbHVlIjoiMElhQXlBSERkNDJzWStLWHhEcDB3bTliM24xMDM1Q1Q4M3NLMllVNndSSkJUUGlBb05LV2FjQXZST1NWUWc3N0NQYWYxRmdIUUViNlV1S29ud3picWY2a3M4cjhMcjg2NXg5NUJkQWNDY1hTK2pmNi93L1RFZWVGdlQrWUU4OGpyc3ZTTW9Bb0JDZUlsaWhLcU5yZHN2ZDR6TFBEWXRITS9RYkc5NktYVmxPNEJsekd4alFkODVtSUYzem1DNDUzUlp6aTZoZm5hTURrYlc5UVkzR3FjZ3lCR3h4R1hRMVAxb05CK0d6R0NpOElIKzl1NVBVcXgrVGEvdENJeUNicysyL0dmS2twbzB3M1FjQlFrQ09jMUI2Wk85RkM3YWE2SmhGUzZhOGhVTVMzQzNKL2R5SUYyVXdvU0hXUmVYR25iMnRzNmNFeXY0YThMWHIxa2NndTUvcy9MekVIS1UzcEw1VktMUVdWcHFoaTNlWnRsN2pKZ3ZzRVppQTBuWlNrSERZNXdUSXhjcjJvMzM4czFOU0YvMi9lSWhhdzFYb0pTTEJsOTRoUmM3UlJ1UHlDNlZ4U1VMdXhRWmpROVNybVVGWWc3Tk1KZzZHeFpYL0E2TnBNaGhNcWRjUHd1Z1VVc1BwczVwSVVsaGVTTG14cTdXL0FQYVo5QjdOYzZnbGoiLCJtYWMiOiJlY2NkMTZkYmZlOWI1YzMwMGE0YmQ3MzQwNzIwMDVjMDAwNzBkMzMzMGU4Mzk0ZmUyYzk0NTZlYWIzZTcyOWVmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVHVGxmTkhHQ3g3WU5iWC8yaWxGRHc9PSIsInZhbHVlIjoiNWY2WE9ka1ZhTTZZR0dvU3RsYXgrTmNFMm12aFdweTdrQU5YQittRUdhTTVNMG1vbHhLWHVFb1dFT0pNbXAzUytFenhtSFl1RTliOWE4K0I0ZFNUUktGbUVwOFBKV2hQUHphZTRNcEw2VDNFNVAySUlxM3M3MlBMbDJFekRRSDNpd2svOHZ1Z09tTmpiRjdWRlFsVVpXUTNKWTljcGdpZmVrUzIvbHpaS1M3a2JROVNVYnNrSUs3RHN2SVJrdm9jSUo5cmE0MHcvcHNxSmJDWUM5RVhvczZIMk1QTE1xQUJTUE9zSWpMNEZQbFoxNHYwSTlOaFFYYmc4dmpmTnNzUno2bE45N1hSNEJTN2VtUERQc2pKd05ja2RsRDIwWmR0cUlicVdIRzRIZlg5cE04Y2RxMzVUNXRwYlRwOGxHb3kwTzhjbGVtV2w3ZURLb1YyMjJtZFNzbnZhazZFU21GSHlVQSs1QU5PMVRlOGdFcUU2Mmo5MzIxUkFhdzBTaEd1cGtBcXV4TkJGL1lrcXNzMC9Nd0tRWlkzcDJpc0czQUZiUHA0VE9wSFMrMzYrTTBrdkJHNGtuQmNtTWZaQXdsM25EV1VMYXNlN0VhY3d4dzBLaWcwZ05mVUxEMHhCaFBrYU1Gc0JwV2NhZFloWUFoYzlqMjlmekkrYWtMYTdJTUUiLCJtYWMiOiI4N2ZiMjk5MDZlNGFkNzM4Nzk5ZWY1Nzc5NDE3NmUwYzE4NjQ1MjY4YjM1N2E3NmQ0ZDY3YzljMmQ3MDY2ODhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228545336\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-901083974 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BvzDAp8Ceepvt22TODQJA2h22xCzbnkTzOjAbsGW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-901083974\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1204980911 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:34:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikl3aFpMUVduOS9JL0p4NnpHdTNManc9PSIsInZhbHVlIjoiY0NhUXpmZm5JQURtZjRYVEUza3hnN3QvSmljem9IaUxGUzU3VzA5a2ZVV21id21oSTVoenkwOS9YdEhyUkxlZmJhbkE5K2YxejR2Q3ZDbHY3VFhXbXpaS3hqTVE0dERtNWhBYXYxVmx6aDdickdUbDZuVXBlTGkrdVd2OEdZV0IzNDRQUVRSU0dxUnhtVDhqeU43M0NEbC9zK0lFYTROQ1hycEtaTnNyOTFsVkVLQ2dnTmRHWmt2TElpdFc0U2xuWCtEcmpjaVcxZjNuby8zaWR4WFpxSkY0dW5mejA4YU9tQXpjd0JiRWdSOWw3WGIxcmFqRktoOW1KenM1UkZtUlFqeDJFRUc0TFQ4OGlSNFVXekpacFNBSFBPeS9odEJSdzJtMlJvZktJcjZKTWEvaTFKbDR4SjB0dHhSTEZpL2xFREJqdXZzeEdqbFBOTi9SRVVmb290VlpkV2IvQTlJN1BjaUdRL0x0blVOMjFXYS9NU1FIcWFvdWN1SXE0WnBvMHhHODVFQUIyY2dZZzdyclFud1dVOEJvUEhsbEhRV0hVd3puaTVnOCtlRHovM2kxbGJwdlZ2bkdqZy9PUmhid2U3Wk5ONEhXeGJHNFlVS3VJdTBrZFlmMG9wSzJqQ0xRWGRqRWlpSm1hZ200ZEtUNEFGUzJPUjh5Umkva3JtcXoiLCJtYWMiOiIxY2U5MzQ0ODJiOWQwOTBjY2Q3ZDMyMWYyMjVmOGU0YjA0MDUxMGUxYzAyOGVlZjY1ZGQ4Y2FhNThhMmU2NDNmIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:34:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxicDVlNlc0aG9zaVY1THVCd25pWVE9PSIsInZhbHVlIjoiOCtyTzVGZnJVSWRsWW9XcVRRRFdXZnk0M0MvNDJUTm1WbS9TancxYnVzZXZ1QkhDeDFCOGk5bmk1UzVQV1JuT2JuZE5ZdDBwL1BMYWlrdGpTUlZ4TkhWMzYrSkM3SWVoVEtLL1BTMEhmL09kOWNkbzVXcUx0QzhHMVE3SHprUHo5RjRKc24xRE9MOTNMWnI2Q2lFNjNFc1NUMWpPWXpSLzJWTWlGWlo5YmFTRU5EQlpVeXo1TlVHRmJtZ1RiNDFzMUtHL1VJU0JTcDF4MHhJemJZZUlnV2YzaW9iVGF1QmR2ZENjTEZzK3RlNW15M3BWN245SmkrSlk1dHBzbzA2MXcyV1VTbDVEWWYrL2REMGdzQmhkd1NYeXpUeENKWG5Pd2UvUmtNdU8weXJwVWRQRlgwRUgvdDIwQjJ4WnNVMzR0bG9HL3diWXY4cEVkem04cVJRNi9YT2s0SXh3NE96Ni9NSHJTNmI1S3lMU3F4eU5Na1hnNDZVWjdLN1F2WmFLVmU5NmVyQURGWXV3TnhHaTRIUWRHb2Q1T2FrWXVwWTgwSDNhbW5wWUxoZ3BKN2dwREZJZXRTMGpDVEZJMnhiQlRMYTlGenhPNndtSWE4YVJnNHRKVkJsRGhSaUN6MnVWVjhUTUQ2RytRaFY2emZ4cmZzMjF3WXRCSi9zMFBoRXciLCJtYWMiOiI4ZjJkOTU5YTA4NGI0ZDliYzIzYjJkYjM5MTNhNjM4ZTZkYjZhOWNkN2RiNzViZDYzZjExNTExZTRkOWI1OTI4IiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:34:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikl3aFpMUVduOS9JL0p4NnpHdTNManc9PSIsInZhbHVlIjoiY0NhUXpmZm5JQURtZjRYVEUza3hnN3QvSmljem9IaUxGUzU3VzA5a2ZVV21id21oSTVoenkwOS9YdEhyUkxlZmJhbkE5K2YxejR2Q3ZDbHY3VFhXbXpaS3hqTVE0dERtNWhBYXYxVmx6aDdickdUbDZuVXBlTGkrdVd2OEdZV0IzNDRQUVRSU0dxUnhtVDhqeU43M0NEbC9zK0lFYTROQ1hycEtaTnNyOTFsVkVLQ2dnTmRHWmt2TElpdFc0U2xuWCtEcmpjaVcxZjNuby8zaWR4WFpxSkY0dW5mejA4YU9tQXpjd0JiRWdSOWw3WGIxcmFqRktoOW1KenM1UkZtUlFqeDJFRUc0TFQ4OGlSNFVXekpacFNBSFBPeS9odEJSdzJtMlJvZktJcjZKTWEvaTFKbDR4SjB0dHhSTEZpL2xFREJqdXZzeEdqbFBOTi9SRVVmb290VlpkV2IvQTlJN1BjaUdRL0x0blVOMjFXYS9NU1FIcWFvdWN1SXE0WnBvMHhHODVFQUIyY2dZZzdyclFud1dVOEJvUEhsbEhRV0hVd3puaTVnOCtlRHovM2kxbGJwdlZ2bkdqZy9PUmhid2U3Wk5ONEhXeGJHNFlVS3VJdTBrZFlmMG9wSzJqQ0xRWGRqRWlpSm1hZ200ZEtUNEFGUzJPUjh5Umkva3JtcXoiLCJtYWMiOiIxY2U5MzQ0ODJiOWQwOTBjY2Q3ZDMyMWYyMjVmOGU0YjA0MDUxMGUxYzAyOGVlZjY1ZGQ4Y2FhNThhMmU2NDNmIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:34:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxicDVlNlc0aG9zaVY1THVCd25pWVE9PSIsInZhbHVlIjoiOCtyTzVGZnJVSWRsWW9XcVRRRFdXZnk0M0MvNDJUTm1WbS9TancxYnVzZXZ1QkhDeDFCOGk5bmk1UzVQV1JuT2JuZE5ZdDBwL1BMYWlrdGpTUlZ4TkhWMzYrSkM3SWVoVEtLL1BTMEhmL09kOWNkbzVXcUx0QzhHMVE3SHprUHo5RjRKc24xRE9MOTNMWnI2Q2lFNjNFc1NUMWpPWXpSLzJWTWlGWlo5YmFTRU5EQlpVeXo1TlVHRmJtZ1RiNDFzMUtHL1VJU0JTcDF4MHhJemJZZUlnV2YzaW9iVGF1QmR2ZENjTEZzK3RlNW15M3BWN245SmkrSlk1dHBzbzA2MXcyV1VTbDVEWWYrL2REMGdzQmhkd1NYeXpUeENKWG5Pd2UvUmtNdU8weXJwVWRQRlgwRUgvdDIwQjJ4WnNVMzR0bG9HL3diWXY4cEVkem04cVJRNi9YT2s0SXh3NE96Ni9NSHJTNmI1S3lMU3F4eU5Na1hnNDZVWjdLN1F2WmFLVmU5NmVyQURGWXV3TnhHaTRIUWRHb2Q1T2FrWXVwWTgwSDNhbW5wWUxoZ3BKN2dwREZJZXRTMGpDVEZJMnhiQlRMYTlGenhPNndtSWE4YVJnNHRKVkJsRGhSaUN6MnVWVjhUTUQ2RytRaFY2emZ4cmZzMjF3WXRCSi9zMFBoRXciLCJtYWMiOiI4ZjJkOTU5YTA4NGI0ZDliYzIzYjJkYjM5MTNhNjM4ZTZkYjZhOWNkN2RiNzViZDYzZjExNTExZTRkOWI1OTI4IiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:34:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1204980911\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1471291896 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1471291896\", {\"maxDepth\":0})</script>\n"}}