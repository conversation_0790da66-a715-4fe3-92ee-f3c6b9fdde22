{"__meta": {"id": "X1d81f7d16375133eca42b0f93034b46a", "datetime": "2025-06-19 12:33:17", "utime": **********.187599, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750336395.325041, "end": **********.187626, "duration": 1.8625848293304443, "duration_str": "1.86s", "measures": [{"label": "Booting", "start": 1750336395.325041, "relative_start": 0, "end": 1750336396.940431, "relative_end": 1750336396.940431, "duration": 1.6153900623321533, "duration_str": "1.62s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750336396.940462, "relative_start": 1.6154210567474365, "end": **********.187629, "relative_end": 3.0994415283203125e-06, "duration": 0.24716687202453613, "duration_str": "247ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44442792, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.06531, "accumulated_duration_str": "65.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.086441, "duration": 0.06531, "duration_str": "65.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3gqeZZezc7HcpITdlxCIvAYQRFtc666F4ikPxabr", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-993549476 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-993549476\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-634169533 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-634169533\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-168448755 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"194 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-168448755\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1216621824 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216621824\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-192940326 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:33:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlZbzVCVllSaWl3cms3RU5nRE10enc9PSIsInZhbHVlIjoiV3diVCtBQStTb0djOVIrQUdjdXBocDdLS1RTMWJmQmVkWHBFbmtTSTI1YzFMaDgwVUhVdlRCalZnZHRFbTIvZkZXTlB1U0NaeVVoVkxhVmNxclBYRHRrVVFHYU5kRENiTXdEQi82VC9SVHJaMk15MkZEbXVDMVVBeWNVRWdZWEhGSkRncnA0OUJ5WUdLVzlsdjVlYkQ0U255ZUFScmoxV29NeFZvMXZGZ2VCMzE1Ykt3VkxTQThGcmtzSExtUnZtYmp5UXNmanFaWkE1NkRtTS94cFJYbExYM3BiTndCeGdkVCsvSDdualNQM09pcDRacStBUFFJNzUwUDdqYXJMUXdVbVFpUWNlcjdSS0tBNGM3OHJtWXZwQzh5ZEZnQlBORlFaZjFHSXpHbGs2M2hRSGttMmJkQlA5RnNlbHRpaEZUK2tQRStoaytwaXlzOWZQbjdWTGRHeXhaYnpoNStOT2VSdmRXaWQwWHZhZ3hJekp5cTR5S0N3Y2k4OGh5R2NsUU12aWhiYjJlNGR0Z2xsZytYcVlMZGwwQVpTTVhoamxydFY2TEw4aVhSL3pSMFRuSmROeHlRZmJkbDBZZDFZckczRFFIK0dIeEIwTVdleUl6WklrbmxPcnpqaXVVUEV2bFJRYXgwRG5Ibjhzc3kwMzZ0V21RSmlaTkVQUFBWTTkiLCJtYWMiOiJhNWFkM2FjMDhjYTI2MGMyNTc3MjhlYzNiNzJmNTIwMTFiYjhjZTM3MDRmYzc2ODUyNzRjMTJkMzJiZmI2YjA2IiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii8xTCtxNWxTenRhSENkU0VFWEplY2c9PSIsInZhbHVlIjoiWk9waityUVFEdmJ2WExNZDhYOHp1VnRzUVJQeWJFOGNzbmFqSVpmMU01UnFvNnZMNTBBdGUrb1UwKy92SzI0clF6eW1kY3ZieFlQdzBaaWtLdXZMUUh4MmtiQjZFV3d3Q0hPdGlNQ1FxeFBuOVg2UkZ3STJUcUVVckliSG15ZldPOVFvMnNmRGhDNzVwZEpIVFBOWC92QlZtMXc1U3ZXU05VSkFqTUhMTDFBbzJjdjFkWkx5YnRPR0Z0akNYK1dpMkRiVm5rQnFBRVJMeUFzYTRJb1ZOWk55ZGg3YnlWZmo4V3JIR3cvaHJPOUU5S29XQUFaR2JpazhxSWJTVGtqTHFxUlYyN1JpVWRGMDVqTzRkeFMvYzF5SUJZSVErTVpVdXRDczZxeHVGTnZHK0xBVlZZdHRtck8yMmRIY1NEZCtZYXp1bFNrdmJwK0pWR1FxYk1VTTBTUWNSR2IvS2dFRllFejJLdHlBWWl1TzN2RXNWZCtuOW8xZk5wZ3U0aTAyUndkNVpMTGJXZGNqQXVDS1BsQ0poa2EzbnU4NUg5S29DRFFqOXhkNGVsQkJZTWJSME5adWo2YWp1akJSSVozencwcG5vYlJ1bjBaeTZiQ2FHMFFDV3hkOEhwZUk3OG4rbHNmL1A5UVNVeDRNeHJ1WmJaM0JHSzRSMHZjMEFBR3oiLCJtYWMiOiI1OGU0OGYzMTEwYWI4NmRjODFiZjY0ZGQ3MDgwMzVjM2I2ODk3MjUzZDgyMmU2NDEyMzc0YTg1YzJkNjdmZGRlIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlZbzVCVllSaWl3cms3RU5nRE10enc9PSIsInZhbHVlIjoiV3diVCtBQStTb0djOVIrQUdjdXBocDdLS1RTMWJmQmVkWHBFbmtTSTI1YzFMaDgwVUhVdlRCalZnZHRFbTIvZkZXTlB1U0NaeVVoVkxhVmNxclBYRHRrVVFHYU5kRENiTXdEQi82VC9SVHJaMk15MkZEbXVDMVVBeWNVRWdZWEhGSkRncnA0OUJ5WUdLVzlsdjVlYkQ0U255ZUFScmoxV29NeFZvMXZGZ2VCMzE1Ykt3VkxTQThGcmtzSExtUnZtYmp5UXNmanFaWkE1NkRtTS94cFJYbExYM3BiTndCeGdkVCsvSDdualNQM09pcDRacStBUFFJNzUwUDdqYXJMUXdVbVFpUWNlcjdSS0tBNGM3OHJtWXZwQzh5ZEZnQlBORlFaZjFHSXpHbGs2M2hRSGttMmJkQlA5RnNlbHRpaEZUK2tQRStoaytwaXlzOWZQbjdWTGRHeXhaYnpoNStOT2VSdmRXaWQwWHZhZ3hJekp5cTR5S0N3Y2k4OGh5R2NsUU12aWhiYjJlNGR0Z2xsZytYcVlMZGwwQVpTTVhoamxydFY2TEw4aVhSL3pSMFRuSmROeHlRZmJkbDBZZDFZckczRFFIK0dIeEIwTVdleUl6WklrbmxPcnpqaXVVUEV2bFJRYXgwRG5Ibjhzc3kwMzZ0V21RSmlaTkVQUFBWTTkiLCJtYWMiOiJhNWFkM2FjMDhjYTI2MGMyNTc3MjhlYzNiNzJmNTIwMTFiYjhjZTM3MDRmYzc2ODUyNzRjMTJkMzJiZmI2YjA2IiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii8xTCtxNWxTenRhSENkU0VFWEplY2c9PSIsInZhbHVlIjoiWk9waityUVFEdmJ2WExNZDhYOHp1VnRzUVJQeWJFOGNzbmFqSVpmMU01UnFvNnZMNTBBdGUrb1UwKy92SzI0clF6eW1kY3ZieFlQdzBaaWtLdXZMUUh4MmtiQjZFV3d3Q0hPdGlNQ1FxeFBuOVg2UkZ3STJUcUVVckliSG15ZldPOVFvMnNmRGhDNzVwZEpIVFBOWC92QlZtMXc1U3ZXU05VSkFqTUhMTDFBbzJjdjFkWkx5YnRPR0Z0akNYK1dpMkRiVm5rQnFBRVJMeUFzYTRJb1ZOWk55ZGg3YnlWZmo4V3JIR3cvaHJPOUU5S29XQUFaR2JpazhxSWJTVGtqTHFxUlYyN1JpVWRGMDVqTzRkeFMvYzF5SUJZSVErTVpVdXRDczZxeHVGTnZHK0xBVlZZdHRtck8yMmRIY1NEZCtZYXp1bFNrdmJwK0pWR1FxYk1VTTBTUWNSR2IvS2dFRllFejJLdHlBWWl1TzN2RXNWZCtuOW8xZk5wZ3U0aTAyUndkNVpMTGJXZGNqQXVDS1BsQ0poa2EzbnU4NUg5S29DRFFqOXhkNGVsQkJZTWJSME5adWo2YWp1akJSSVozencwcG5vYlJ1bjBaeTZiQ2FHMFFDV3hkOEhwZUk3OG4rbHNmL1A5UVNVeDRNeHJ1WmJaM0JHSzRSMHZjMEFBR3oiLCJtYWMiOiI1OGU0OGYzMTEwYWI4NmRjODFiZjY0ZGQ3MDgwMzVjM2I2ODk3MjUzZDgyMmU2NDEyMzc0YTg1YzJkNjdmZGRlIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-192940326\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-735683552 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3gqeZZezc7HcpITdlxCIvAYQRFtc666F4ikPxabr</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-735683552\", {\"maxDepth\":0})</script>\n"}}