{"__meta": {"id": "Xf6c2fb3d51d1a390be308ea7f1e36d5e", "datetime": "2025-06-19 12:33:36", "utime": **********.634799, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.813897, "end": **********.634821, "duration": 0.****************, "duration_str": "821ms", "measures": [{"label": "Booting", "start": **********.813897, "relative_start": 0, "end": **********.476952, "relative_end": **********.476952, "duration": 0.****************, "duration_str": "663ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.476967, "relative_start": 0.****************, "end": **********.634823, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01769, "accumulated_duration_str": "17.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.534836, "duration": 0.00897, "duration_str": "8.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 50.707}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.560044, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 50.707, "width_percent": 9.44}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.614189, "duration": 0.00705, "duration_str": "7.05ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 60.147, "width_percent": 39.853}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336400672%7C1%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlplTjJwdWxPTk5uVmtiYjRIYnl3ZkE9PSIsInZhbHVlIjoiVE8yVkM0Zkt0VGxFL2ZQV0ZhN3Y1RWVEZ0ZmcitXTzlvblVlREhYL3dFOWFXTkZWaUtZZkI3ZDZjanBDY0ZkNzZrUEFqdndNM0Znc1lYOGduQzF0ZmNBMmxTdVFiQ0gzREs3RlJVSWs4QTlwdVhwQ2VTditaRDh5VVhXd2ViQU1keENGZWE4WVhTRCtzT2ptKzIzRmlpNGFRdkRHU09XdHN1QjRONzVJWXE3WW1oYmEyY1pMRmczalVXYXpwZVhpSHJyVFFreVlaMjJpNzZBQXg1akE4cEJEaVlUMTJ3SkNyTjVRT2FNQm1pek1OQ0cvZ052SVpXS0JtR0NuVmo4bTVmUmdhR3JEcWxzZG03K1g1cHJYckU4N1h6YitWaFFYU3lsaXppeVdUOHJQaHNhQmovdFE3VUZvY1IzNVhlbzdwNGt5bUwxUERuTVYxYVcrb1o0ZlpIOE9EYUVKUS8vdklaZ3k1NzBpbXoxVGFvdFBEYVRXTW9VVk5sRnZRRUFhWUtnRmcva2NZcmFGZmRmczZxTUlpYVRXSkt6Zm1sSEljRHFmTnJzTEt2djZQSURhZ3YvU1VvUEtsdWZGWWlZZDJaRzVoaEhkSEdYMlBwQzVzUmp2c2Z2QXlnajJwam5DajJSMHR1bjNtcWlTOXEveExlbEVOOUpMMWVMYUZVcGIiLCJtYWMiOiI0MmU2MjUyZmMyZWJlNzhmM2U2MGI3ZjM3ZjVlNGRiY2Y0ZjY1YWY5MTNhM2U0M2JhZGNhNjFiZDE1NzdhNTZkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImtYUHRaR2l1b1doTkNuUnpqWlh6S0E9PSIsInZhbHVlIjoidzk4S1NXdU1IUlNmYzZHNGh1MVg2V0NFT1BpV3hDcmZVM29SYWg5WDFYTFNoSjVaUjlzK3o2MWpLUTYzODZNekJ5aC92dGtpRUtuTno0aG1kWkZ6cU5iRCs0TlVpbnZIVXErV0o4WlpJMUpIZU5FcVpEbUQraXlnd2ZDQmJ6cm1GVVg3a3IrYlc3TlVRVE5xOFg3UVVvKzJCaGV2ZTl3bFRTMXI1S2w5VWh6Tkw4aWJSaXcrL3RidDQ5ZFQ4bWpBenpjNE51bndKUVF0QmZET20ySlU1TVB6emdVaTNJRHBNRVEzMzVaakhFWm9LT1JLTUUvSjFTVW1Qa0tPYzhCeksrNXI4UmV3V2NKYnJyTERGOW1NcFlmUjVlclhCVlp4OUlHVkpoQzY4NGFmemo5dUZzN1F3UzNKVXN6VEVrMEMyZUJsRzdYeloyV2t5OE5CNktGb3FiQnBYU0x5UGZYRlVVbitOUXhnZzNkQ2N1d1V0RHhldFkrZFFhYmRidm54dThMWUVSNDJxei9oMVN2YnRYN2lrSHpuZTNQdXA5em9nYnBUUzRuOERwYjB5ZXNsRXpHY1B1eDF6aXVXWk9sQmkvWkNUWWJRNTBaaDEyZGJ5emtJeTkzWjI4Vjd2YXVSblJRVzgwcDArMFg3d1NjcEtaSlRJNjcvQzUybTM2aHciLCJtYWMiOiJlNzQwZjA5MWVjNmY0M2IxNDE4MzJhMTVlODg0MjA1MWQ2MGM1MDU4NjFmNjI0M2FlZTYwNzdkNmM3ZThhYTNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lYCgmFS8fqkPjKv2BfRzZo9jiALUMXID3HgtHMZN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1869772271 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:33:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRwUm5kYmxtd3BrVGx1S1hkWCtuQmc9PSIsInZhbHVlIjoiSC9ZY0tNSGRhWSt1SXlGTlpoaktSRGtscm5LcmtRb2hJd2htdWFLdHhCVGdMTWU2WFZ4Zmd1b2V0UWUreUQrL0VEZm92SzBjSlM4REw4cmxrbkI2cmxSaDJodGYrR3V3N1ZScHE4bGVJVEJnTENNMUVZMGpCOXRPaEFrYVJITEpnbm1hVDUwUXR3ZWorZzJhcjNxbWlqRkZmbTlyejlRdHR1Y0lXZ3VJR0VIZDlnTndPVDIzcHJiMnRFUUJXS3ZiUFhubGRjUFViSWYwMVdpREhsRzNrRUpEd0dCOWF3V1JxUWp2eCs0RVc3b3EzVWxuSmc0UFNjaWpHb0xMR2dRamJsNXNSZ2I0RWxscXZ1SlZpV0xBSjZSWTJmNTVFQllkc0RWNldLbHhtMEYxNVY3SFh5SUpDNXh1bExxa2NFalI5bEIvZllZekl4a0ttbFd5R2RvY1RWQ2p0OTBSaCtoWnVGcERoQ3d0dHladGtXT1lxSUpYRTl6azd0ZDg2Q25KbENIOVBKMldIMk5malgrSTlaYk1nMEQxY2Z4L1htK3c2VVB3dzh0MlF2b1R4QVhrQXVORTI0ZDM1QkVUT3VST3BVRk5LUnh3UDl2TTcybHU3YmlLTVdQN1Z2c2FaZkV1WVM1Y2VKeFl6bm82MG1UMjVOb29mcDBUNjVEQnJ1eTAiLCJtYWMiOiIyNTNmNmMwZjk5YWZkNDNjNWZjODMzYTM4NGMwYTA2ZTFiOGQ0ZmQ3N2E1MTU0NDYwYzM1MmE2YjgyMjkwYzk1IiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjhWZ3RRemZ6VEYzbDB4WFk5WWFvVkE9PSIsInZhbHVlIjoib1pJNng3T21kOEsreXBzMHVSemx6bjlKVG1vWTBkUUZ2K3h2WlBEL2p1SzFTNE1uY0lhRHUwZjZuMDJMbDdadElWNGkzRW40Y3R3RFA4V0J1Vk9uM1NwV0E5ODZGVnZMajcwVUNNUTFCWFREcjByOWRBVXZ5Sm9ESC9SdmVIaGh0d1lIdzFyT3BhSzJNTnEzdnFrVmdyQ3pZVFFhaWs0Yi9veGxrVCsrNFFCZS9kUWNHTXRlNmRMcmhWSms2NGVvZkxYdDJkMDJicGFUVFNtaEFVVmdZcUdEd2V2TFhBVVREYkdaSWRHUUNVeHNtNUtHK1ZVVHdSbzNGUWJWUUJUZENBTU5wVGtRcWhZR3ZYS3ppNFM0Tk1jVWd0TDZ3eHhjUnhwaWFvSlpWaS9hcVlINkcybnVRQXNJbkNwSkdJZDVWUjhRNms3V0VYeXp6NWFHTHR4R0cweURZK3g1Q05wZ0dWelRUV0lIRHBwbGxNTDVndzVoT0JzMTVpcEtEaHQ2MWlUZWx4OHlQL05URnBhZjlvWVRnTUd4dlAwTzRQVno3ZUtWR1dSbFJlNm9xS0pQY05vTGVGRHFTRGlRYU9ZdEJPdkJFWEdhaVFrMVlRTEtraEp3ZG4rTjIweEpHUlZPMDQ5eUhVSXU4VThVSmdRdWFGczFQMGpNQVBQQW9NR2oiLCJtYWMiOiJhZGQ5MzVkMjczYmY1MDcwODRlZDMxODE4ZGE5ODdmZmI2N2ZjZDY1NzRiOWQwMTU2MDYwMTIwODI1MzQ3YzUzIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRwUm5kYmxtd3BrVGx1S1hkWCtuQmc9PSIsInZhbHVlIjoiSC9ZY0tNSGRhWSt1SXlGTlpoaktSRGtscm5LcmtRb2hJd2htdWFLdHhCVGdMTWU2WFZ4Zmd1b2V0UWUreUQrL0VEZm92SzBjSlM4REw4cmxrbkI2cmxSaDJodGYrR3V3N1ZScHE4bGVJVEJnTENNMUVZMGpCOXRPaEFrYVJITEpnbm1hVDUwUXR3ZWorZzJhcjNxbWlqRkZmbTlyejlRdHR1Y0lXZ3VJR0VIZDlnTndPVDIzcHJiMnRFUUJXS3ZiUFhubGRjUFViSWYwMVdpREhsRzNrRUpEd0dCOWF3V1JxUWp2eCs0RVc3b3EzVWxuSmc0UFNjaWpHb0xMR2dRamJsNXNSZ2I0RWxscXZ1SlZpV0xBSjZSWTJmNTVFQllkc0RWNldLbHhtMEYxNVY3SFh5SUpDNXh1bExxa2NFalI5bEIvZllZekl4a0ttbFd5R2RvY1RWQ2p0OTBSaCtoWnVGcERoQ3d0dHladGtXT1lxSUpYRTl6azd0ZDg2Q25KbENIOVBKMldIMk5malgrSTlaYk1nMEQxY2Z4L1htK3c2VVB3dzh0MlF2b1R4QVhrQXVORTI0ZDM1QkVUT3VST3BVRk5LUnh3UDl2TTcybHU3YmlLTVdQN1Z2c2FaZkV1WVM1Y2VKeFl6bm82MG1UMjVOb29mcDBUNjVEQnJ1eTAiLCJtYWMiOiIyNTNmNmMwZjk5YWZkNDNjNWZjODMzYTM4NGMwYTA2ZTFiOGQ0ZmQ3N2E1MTU0NDYwYzM1MmE2YjgyMjkwYzk1IiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjhWZ3RRemZ6VEYzbDB4WFk5WWFvVkE9PSIsInZhbHVlIjoib1pJNng3T21kOEsreXBzMHVSemx6bjlKVG1vWTBkUUZ2K3h2WlBEL2p1SzFTNE1uY0lhRHUwZjZuMDJMbDdadElWNGkzRW40Y3R3RFA4V0J1Vk9uM1NwV0E5ODZGVnZMajcwVUNNUTFCWFREcjByOWRBVXZ5Sm9ESC9SdmVIaGh0d1lIdzFyT3BhSzJNTnEzdnFrVmdyQ3pZVFFhaWs0Yi9veGxrVCsrNFFCZS9kUWNHTXRlNmRMcmhWSms2NGVvZkxYdDJkMDJicGFUVFNtaEFVVmdZcUdEd2V2TFhBVVREYkdaSWRHUUNVeHNtNUtHK1ZVVHdSbzNGUWJWUUJUZENBTU5wVGtRcWhZR3ZYS3ppNFM0Tk1jVWd0TDZ3eHhjUnhwaWFvSlpWaS9hcVlINkcybnVRQXNJbkNwSkdJZDVWUjhRNms3V0VYeXp6NWFHTHR4R0cweURZK3g1Q05wZ0dWelRUV0lIRHBwbGxNTDVndzVoT0JzMTVpcEtEaHQ2MWlUZWx4OHlQL05URnBhZjlvWVRnTUd4dlAwTzRQVno3ZUtWR1dSbFJlNm9xS0pQY05vTGVGRHFTRGlRYU9ZdEJPdkJFWEdhaVFrMVlRTEtraEp3ZG4rTjIweEpHUlZPMDQ5eUhVSXU4VThVSmdRdWFGczFQMGpNQVBQQW9NR2oiLCJtYWMiOiJhZGQ5MzVkMjczYmY1MDcwODRlZDMxODE4ZGE5ODdmZmI2N2ZjZDY1NzRiOWQwMTU2MDYwMTIwODI1MzQ3YzUzIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869772271\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14********\", {\"maxDepth\":0})</script>\n"}}