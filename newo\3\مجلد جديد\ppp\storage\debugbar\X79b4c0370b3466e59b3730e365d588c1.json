{"__meta": {"id": "X79b4c0370b3466e59b3730e365d588c1", "datetime": "2025-06-19 12:34:55", "utime": **********.441927, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750336494.455624, "end": **********.441952, "duration": 0.9863278865814209, "duration_str": "986ms", "measures": [{"label": "Booting", "start": 1750336494.455624, "relative_start": 0, "end": **********.31662, "relative_end": **********.31662, "duration": 0.8609960079193115, "duration_str": "861ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.31664, "relative_start": 0.861015796661377, "end": **********.441954, "relative_end": 1.9073486328125e-06, "duration": 0.12531399726867676, "duration_str": "125ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46093600, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02771, "accumulated_duration_str": "27.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3707051, "duration": 0.02572, "duration_str": "25.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.818}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.41409, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.818, "width_percent": 3.717}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4267788, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.536, "width_percent": 3.464}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1298925243 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1298925243\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-231924856 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-231924856\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1102085429 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102085429\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1269541825 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336490739%7C6%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhwTmdicHN6cmhUdnBCZlVweis1V3c9PSIsInZhbHVlIjoicnhVTktzR0Jpb21PZHNZaVV3RHZRMTJSa0VmcFFacEI0dlVKTUVBaHVWeE5vWEs2NlRaMEM0aHJodTlHNXFRbkp3WXNmTEEzcGNXcjVWaXZBMG1ycUdjSG5pUkVMSFpuQ1J2MHNTaXF4eXB3cWFheGo1bk1hbzJVbHl1MXlnQmtVd094SXQvUll2K1VqWHhrQnBvNi8wT3VpZDJCc3RWZGUwMnYzamt0SFR2N1hCc1VtZVBaZXNZSFFHZWZRam5xbGlYbmZXU2F0NnM0aXFKZ3dEZkhpSHZweGcwZTYrNVdBd0dwV0dJSjlvU3N2cFJGTWFtc204aHhMZCtoZGl3UXJXaDVFRVd3L016OXlaYjFaSXp5RnZNRVNTa215VXVBQndWOGNCNllEUTlqQzMyYWdJS0cvRWJ4KzVDRFJKWGd0eDVuTkV0S0dLaE0rT1M0V0dKVm41bkI1SHliNnd6NWxlNXhHNGltWTZHYkFTdEpYbFBNMFVpc2lVb2ExQWVWeDBlS0l3ekVZMzVuMEN1bGducXJTK0pWdDFCM3QxOGxIMWIyUTNSb0lGbHJ5aHlaSlA4RDlDM0xIT3hhMmtTM1drMEgveUFLZFJCYW5BdE8xT25Udk9OalYySnBTNUZEeGxsTzZZRUM2SGM3YTQ0clFxaEpuNDQ2am9nbE9WYWkiLCJtYWMiOiJiNDliZDA3MThlYTVjOWZhMjVjNzcyZDkzMjA0MDNhZDgzZGViNjEzNGFmMGM5NzQ3MzRmMTU5ZTFjODU5YmJiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImkxbHFNd3MwMnQ0dnFHSW9RRTVuMXc9PSIsInZhbHVlIjoiYTN2NUt5Q2lQb2NCS1VoVHAwdmtZakdCY0xudks4ZGNQa0U1REx2K25obFVUQmk3b0pmQm5RL1g4c1F2L1IxRGovTDhENm5SbUdySjU3dnFMdVBySzN3VTJ6ckxtK3dLUTVrcDZMZlZwS2JodytzQUVCKytpWVUxVGpIcE1FMjlvekpkSGZWbHdUTWdrVFV0c1ZCWHNsUVFLMnBCblhyeFZpVHhuTmNUUWtIMUhZVGRmT0JMa21mNUtnRUtUQUxLT2J3SFFxVHlIcDNMZWlXTlJmOWwrSDV6S2ZUQjcvSTJPUlhCUHhCUlMxNDd5Q3drdFZneFlydDN4U0hrR0VoRmhyWGw3WmxLMHhHdzFzR3JLaDZyRVQ5Rnh4S2RFWDA3MkxMZnd6OHY2OWsrMmFNMHdNYnBJSzZNK2Y1RGQzaEM0S2p4VWdVMEtPMEptWTlNaEtENktHNk5mTndhbm1VMUNDaWw3S2dQWjFMVW9jZ05PdVZ2b1Nmc0hwdkVqNlZzUnZsYzgrY3AxUUNLaUVkNzhhT3JKUEY0eCtSanc5UllOeFpnZ2dXZW1BZ0tvNzJGRU81bm8wN1VDeTFpVzAva3d3TjYxZHRYVjdKOW9sZDBNTWtzenBEa1dFeTYxNkFMSTVLMFQvd1orUU0xK1BWRTJjcGxCWUZ4QWxRZTM1K2MiLCJtYWMiOiI2MWQ1MmIzZmY2MDkzZDQ5ZGViZjUzM2U3OWZiODY2YzIyODA2ZTBiOTNlMjY2YWQ1NDcyMjMzYzQxZjIxNDg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1269541825\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-464034399 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BvzDAp8Ceepvt22TODQJA2h22xCzbnkTzOjAbsGW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-464034399\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-472287613 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:34:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImY2a25YbDdRMkVwdm92eUhNK1ZZemc9PSIsInZhbHVlIjoiSGJCOXlOVEEzVkkxR2ZRM3cvOU1BOFNnY0lqdGxrbUFwWSsvYThsa2FvS0dJeFp2bXhiQlZnYmQ2MUtiMWxDeDF0UlJCOWlDc0xsTGVCdzVzc0xGK2lRVFFsclVPMko2SDNtbVYxQnpTU0hrMm9vaGxET3cwbmFwUlJNUEV0OGdmT0g1d1NoaDFrQVpHdUFPTWliTGV5VmtZNGV1aDVkNnUwWUZSbEFVTlpnZmlQVGNGTXVxTUJYb1dsZkc2ZDVlMjBhMGpiUjZwYU9zSGxDa3VMQ0RDdGVxdHd6MENXcTlCaGw5RHJCM2MyekpldGtGQVhDTTkxeUZDN2hjQzZLbHFmcExuTHltRE56OG1lSk9TQ0xCYmNRd1BSeUJEelpGVm0wN00yVVRCZE0rSUlwQXFydkhJQ3lHYS9rS0F3YVU0Zy92YjFpYnF5Z09PKzhyZm5IKzZNaFBLQVpvNTBVM2puZEZaaVBJcHFkejV6cVNaOG5XWHNyMkYyZm9ZMDBUTWFmckVQWEM1eVJick9JWlNPelN2U2lrQTR4U2Jnc1FpbW5wczVXSVJVWXJJbFFBTm1LaCtZVTlsQlNRU1RQaUY2dzViUzZpYWliSXh0Q0FXbURMeldGSmxwY3RIUXBRTFVOOEdKRGV0UXR3a3JVTitZbE50aWlVTXBZbm95WG8iLCJtYWMiOiI1NjI4YjAyMjQ0MzUxOThlZGE5OTU3YzdlOTc3NGU2YmNhNWIxYWRjZTJmZmJhYjExZDUwYmM0OWJjNGRiNWZjIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:34:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFIU05xbXN4TzBRdmxXU3FGUE5LZkE9PSIsInZhbHVlIjoiU04rRlBCaUhYNk96L2tBVVpGQXdIODViczJGRGdSNXJLSGhTSnA0N1djc00ranVPT1pWRklheGlPY3JGQUlwbFNuYVZBZ2tad2srWmlSdXdaWWt2aW1PYnVCWmtkMTF1U29leDFkMDdhcDE3WUtpeDNvdEdJVmZFckUva3VVb3BNcXpXVXVGaFhRT2FXVis3N0lBOHN3SnF6SHNuNUNneVpBTFV1QTM2ODkzZlFheVZQdm9pS2N5YkVOQklsZ1Y3NjhIVU1IN1B1dER5bkoram5nTkswYko4SzVKRkw5eitmWjJFSTh2Rkp2NTl1bDkzOEhlZGdTRlN6TUwxbVZDd1g2SUdiWEVyZTk4a29BOUJpVUkvYVQveEFYWWo0K2toNUpST0t2WjlWN3MweXJvNnRzQ2Jxa2JOVi9tQUVUMmNTWXJqc2ZIa1o3cHNLQ01oWjRBOFp3ZzhKYS9NN2lWdzA4VHlVNnNRNEtTdTJTenVhc3haWllxaEZlTk1WTUszQWIvRE5tZkJKN29Qa1BPYUJ5L1NrMDZPTURWUHpPOGNrMHR6Yyt0WllkTVZZY3BMRG5nc2tEWmUwelVCd1lQV0lRc0g2b0RkRHFRYVBHNUljd1pIcnZybTdBbzY0Y1FBdWdJdm1JeHdadmt4SE93aTQvZHFtVnRYekJKRUpPbUciLCJtYWMiOiJlMDBlNjZjMzc0MGQ2MjlmYWVmZTFkMDVmM2UyNDkzOWMwN2Y3YTJlMzJhYmFhNzg1Y2YyYmE3MGNmN2I5ZDVkIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:34:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImY2a25YbDdRMkVwdm92eUhNK1ZZemc9PSIsInZhbHVlIjoiSGJCOXlOVEEzVkkxR2ZRM3cvOU1BOFNnY0lqdGxrbUFwWSsvYThsa2FvS0dJeFp2bXhiQlZnYmQ2MUtiMWxDeDF0UlJCOWlDc0xsTGVCdzVzc0xGK2lRVFFsclVPMko2SDNtbVYxQnpTU0hrMm9vaGxET3cwbmFwUlJNUEV0OGdmT0g1d1NoaDFrQVpHdUFPTWliTGV5VmtZNGV1aDVkNnUwWUZSbEFVTlpnZmlQVGNGTXVxTUJYb1dsZkc2ZDVlMjBhMGpiUjZwYU9zSGxDa3VMQ0RDdGVxdHd6MENXcTlCaGw5RHJCM2MyekpldGtGQVhDTTkxeUZDN2hjQzZLbHFmcExuTHltRE56OG1lSk9TQ0xCYmNRd1BSeUJEelpGVm0wN00yVVRCZE0rSUlwQXFydkhJQ3lHYS9rS0F3YVU0Zy92YjFpYnF5Z09PKzhyZm5IKzZNaFBLQVpvNTBVM2puZEZaaVBJcHFkejV6cVNaOG5XWHNyMkYyZm9ZMDBUTWFmckVQWEM1eVJick9JWlNPelN2U2lrQTR4U2Jnc1FpbW5wczVXSVJVWXJJbFFBTm1LaCtZVTlsQlNRU1RQaUY2dzViUzZpYWliSXh0Q0FXbURMeldGSmxwY3RIUXBRTFVOOEdKRGV0UXR3a3JVTitZbE50aWlVTXBZbm95WG8iLCJtYWMiOiI1NjI4YjAyMjQ0MzUxOThlZGE5OTU3YzdlOTc3NGU2YmNhNWIxYWRjZTJmZmJhYjExZDUwYmM0OWJjNGRiNWZjIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:34:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFIU05xbXN4TzBRdmxXU3FGUE5LZkE9PSIsInZhbHVlIjoiU04rRlBCaUhYNk96L2tBVVpGQXdIODViczJGRGdSNXJLSGhTSnA0N1djc00ranVPT1pWRklheGlPY3JGQUlwbFNuYVZBZ2tad2srWmlSdXdaWWt2aW1PYnVCWmtkMTF1U29leDFkMDdhcDE3WUtpeDNvdEdJVmZFckUva3VVb3BNcXpXVXVGaFhRT2FXVis3N0lBOHN3SnF6SHNuNUNneVpBTFV1QTM2ODkzZlFheVZQdm9pS2N5YkVOQklsZ1Y3NjhIVU1IN1B1dER5bkoram5nTkswYko4SzVKRkw5eitmWjJFSTh2Rkp2NTl1bDkzOEhlZGdTRlN6TUwxbVZDd1g2SUdiWEVyZTk4a29BOUJpVUkvYVQveEFYWWo0K2toNUpST0t2WjlWN3MweXJvNnRzQ2Jxa2JOVi9tQUVUMmNTWXJqc2ZIa1o3cHNLQ01oWjRBOFp3ZzhKYS9NN2lWdzA4VHlVNnNRNEtTdTJTenVhc3haWllxaEZlTk1WTUszQWIvRE5tZkJKN29Qa1BPYUJ5L1NrMDZPTURWUHpPOGNrMHR6Yyt0WllkTVZZY3BMRG5nc2tEWmUwelVCd1lQV0lRc0g2b0RkRHFRYVBHNUljd1pIcnZybTdBbzY0Y1FBdWdJdm1JeHdadmt4SE93aTQvZHFtVnRYekJKRUpPbUciLCJtYWMiOiJlMDBlNjZjMzc0MGQ2MjlmYWVmZTFkMDVmM2UyNDkzOWMwN2Y3YTJlMzJhYmFhNzg1Y2YyYmE3MGNmN2I5ZDVkIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:34:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-472287613\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-271226418 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271226418\", {\"maxDepth\":0})</script>\n"}}