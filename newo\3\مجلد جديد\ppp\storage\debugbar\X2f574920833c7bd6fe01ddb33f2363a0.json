{"__meta": {"id": "X2f574920833c7bd6fe01ddb33f2363a0", "datetime": "2025-06-19 12:33:48", "utime": **********.370641, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.565377, "end": **********.370669, "duration": 0.****************, "duration_str": "805ms", "measures": [{"label": "Booting", "start": **********.565377, "relative_start": 0, "end": **********.260023, "relative_end": **********.260023, "duration": 0.****************, "duration_str": "695ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.260042, "relative_start": 0.****************, "end": **********.370672, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015009999999999999, "accumulated_duration_str": "15.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.311368, "duration": 0.01227, "duration_str": "12.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.746}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.339435, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 81.746, "width_percent": 7.995}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3562381, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 89.74, "width_percent": 10.26}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336418704%7C2%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZvbHVjOG4rb2pGVG10QkFyQ3JLb2c9PSIsInZhbHVlIjoiWjlzVzFRME1aZGxEUGtzU09JYks2UHFubVQ2S0N6Qk9mdllzYU9CcDZhRnduRER5dVg0b2luN1hwUzI5TVhiVkdKMlhuNEorY3FSRVFTMERJQjdEQ1FSNlhkVHdmK25MSjNkZU1admFJdDRXK3VHSGpGbGV3d1V0MkxjZko1WDJOZEozc09TZ0FhOVBoYXhpdWVod0xFMU5wVnRvMlhYdmEyTlhDS2ViTmNhQVJVdzJORlhzbnJMeVpJUDVWcUNLUHhNUmgvanZmNnY5akZvemFFUDVKU1ZZNUFQb21TVGs0WFF5cTl5YSt0QklSdlRwRzhkczJWVGlLTGhEd3hXc0JTZzJKazJicmk1MnhOajNDUk9lejVMMG9HZlVzY25PKzFCejRiN0FsZnB3RGhVK0JKZW5IWUtiYzh3NmVwU0VKMldqVm8yZFdIT1grdmNOUkFZdnBwVEZ3Njg3dk10aGVCSjRTUnZRUG5PcUt6UlZvbEIrQStKUGlmdWRGTkZKcGw1ejV5Qnh0NjQrbmlsSWhGbytvdWkxSWxha1V5UzRGaFRDRXI1SnYzN1JUSVlVK3NFbXRUdkVyK1l0N2RvR01rQU1Mc2FOelc1U2VBUHFEWFVlOGNBVk1PenZvZjFhTy8wbmx1VWZRN2l6R1hXaXVkaTFkdG9TZmlvS1d2eUoiLCJtYWMiOiI4YzEzZTE0OWJjODlhODMyYTkxMDgwMzNkNjNjMjM0ZDczMjdmYWE1MWEzYzhiNDY4MmEzMTcyZjRiNjQyNjQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkN1Z2hSb1huTWppWlFDandYQVBRWXc9PSIsInZhbHVlIjoibGorWUhZK0JwUEtXQktPQzhRbFVlTDhySzBFQ21xbU5EZWd0RmN4bDNZQ0VqbGQ4V3BFeVFwNDMwM25WWEtmVzA0N0kxbGdBeUhpbW9pODNlb1Jva1JncE52OW9TZW5Zby9vMU9kNzJ4OVk1aXJJUW9XZENnY2NZRkVpKzRlMGxjSnlXcitEMzRnMWsxeEg1R2NjdGVPWXBKd05DZXc5RXZpOGN6NzM3MTVYa3p6SURNVDNjRU1KUWU0ejJVSy9sbTRHamEwOWNjWjdOQU1nVnErcmpuZXJ2R1NNbGlJUEhueFN0WFFNMVU4MHZseTk0VFZvQkxlRjRJWEZ0NU02a2p3ZkNSVXhwVlFSamV3OEZMR0NFeWlhNG9zOGU3TGg3bGpqbHh3TktrS0MwdTk3eDhCNGNwY2xrSHlRNFZzUDBhNEMyM1dwTDFpaERNQzZJUThCTkFDS0pteTBWb1RNZmlXRjVrZzFMV2s2OExxMEdyWXIvcUNWSHhpRVJKQTlwRmFlUldJeTlUV1luYWtxYlI4NXFDaFdTTVRZVHFEMlNzRm5XUmZmNXZsZkxNSmV5dHQ0U0N2eVB1WTNPU05lOFpXQ0p4RjFkcDJ3bGFZSWQyK083blJoZlptRnNYZ1ZaR3Zod2ZKTlVLZ2puMk04S0JJWWNBMTNNR254K0pPOTAiLCJtYWMiOiI3YmU0NTk0OTFiZDJiM2NkM2ZjNzk1OTcyODFlNmQxNGJmN2U5NmU1MTI1NzE0ZjJkOWE1NDk0ZGRhZGY0MDBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-450849270 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lYCgmFS8fqkPjKv2BfRzZo9jiALUMXID3HgtHMZN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-450849270\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-653784620 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:33:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InYxZnM3ZUtIUTNOdWZlQ2p3WFMvUGc9PSIsInZhbHVlIjoicXo0akVSYnFmSVROczhkQ1h5YU1RcENobmNUOWNGUzJjVjV0SjBIYTJnSnF0dUZ4Zi90a1Fkbjh3S3FuTVZSZnJINzJNZTVIekJnczM2NlFLeVpZR3hLZU5Mc3hlZ3llQnZZelhXTTVLMytEWmpucld5bGNjZHlmK2VvWGFuUDcwVjRNdzI0dkxkRUZ2bVM2ZDJ4QWZSM09JT3hHMU5TM2VtYWFhSmo4T0dqTlBBSG00Wk1UOERDNmxKakRxUDlkbm84bnVwYUFDWGI3NnZFN1Vsd25KajkwN3RNeEpoSk1iWVkvTWhvZjlMLzR1YW8waStIS3hzN1gzNlFSci81UTY0YU9JWmZYRW0vUVM3ckNiVCt3TW05cWh1cXduTy9pS2h5R0taNU9ZSzFJclQ1NUh2OFh1SzJSY1hFNXhYYVZnSHdYTFZSeUoyTWhHYmJKTU9uTFJLVk1ZNHUzTk9iSjRZSnJuSGRRQWQxTm1UQjdMNlVRQzlnZVhhYTFTZXBpS0F3azMrcnkzeTQ0eUFkdmZjZHQxVVloRmI0NkRWUnJnUGJ4ZGk5a0NSa1pwOUx1VHNVMDJWanhTQWFzdDdlbG9WUHRJNnh3K2IxeHdKbXdRclhDNWhJN1hGYk9BYk1ZRnBtSjdJNDBWYVJwRlYraDMxYzBMK3ZpSXpSaDJNMTUiLCJtYWMiOiI3MGQ5MDc2M2UxY2E2ZjNlM2Q3ZWM0NTFhYWY2Y2VjY2RjOGJkYmJhNzIxMjJmOGVhNDYzMDYzNmEwNzVmNDliIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InFmcDl1SEFXdDVwOGI1QWM3Um9SMnc9PSIsInZhbHVlIjoiMCtiNTdKVnRZYnNtL3hoQUpFYU1tcG0zWlNxa2VrdW45d1V3TTNXRFVFOXJKUm1xR1NOMFp1UHVDQ2NpVUZXSG1mZW1Ga2tzQmV4RnhNS3JRVmU4K2RDbzNmSGs4a2FMR2IrdzNhRXZzRUZWYXF2d1dBMWFKNmx1WXJYUXhmS3dNbHZMbmJTMVA5RWF4NGlyeDZTY0FISWZMeXltTU01UUhRbGVhSlhsZ1owRDJkZk5BMU1JVVh4cDVxRXhRZmhRVVpWazFHMEJ3Y2p0ekpGb2FpdG1HNkZsbTFEdXV2aHRYanowOW1TdTBaempLL0puZXFmLzRvYmJZNFVHRE1BYXNmOUtIZFBxWHZOR0JIMy9Sa3htOGJpa2NmbFBTdDl1ZUh4ZTBEVnJwZ3RudGVQWHJuakxQa09QVmpnYlZiQ2xNU1A3Q29rWGNQLzI2dGdKNUpzU3MxR1FNMGJKMGhWdkNRRVZlM2JEZzFSOE5FSDUvcEN2ZVdGN2xJMElGak5KOTNQMWhtMkJmWmV1STFPZ2lka2t6NHBvTU9GZlU2ZE5YSTJHUlZyTGtNT1YxeVBEb2FOb29YaDIxM05waUlGS1JBSFFMWnRFSUM3YmJ4djZwYzh3dHRMSTV1RUx4NDkvV0NsemhwTE56ZWM1Ni9mR3Y0WjBOc0Fkd1hBUHBWTFUiLCJtYWMiOiI4YzFkZmRiY2Q2OTliMWUyNmYyOTdmNThkNGU0YWVlZjlhMWM5M2U1ZjEzMWJiZWMyMWUzMDAyNjNkOTllNmQ1IiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InYxZnM3ZUtIUTNOdWZlQ2p3WFMvUGc9PSIsInZhbHVlIjoicXo0akVSYnFmSVROczhkQ1h5YU1RcENobmNUOWNGUzJjVjV0SjBIYTJnSnF0dUZ4Zi90a1Fkbjh3S3FuTVZSZnJINzJNZTVIekJnczM2NlFLeVpZR3hLZU5Mc3hlZ3llQnZZelhXTTVLMytEWmpucld5bGNjZHlmK2VvWGFuUDcwVjRNdzI0dkxkRUZ2bVM2ZDJ4QWZSM09JT3hHMU5TM2VtYWFhSmo4T0dqTlBBSG00Wk1UOERDNmxKakRxUDlkbm84bnVwYUFDWGI3NnZFN1Vsd25KajkwN3RNeEpoSk1iWVkvTWhvZjlMLzR1YW8waStIS3hzN1gzNlFSci81UTY0YU9JWmZYRW0vUVM3ckNiVCt3TW05cWh1cXduTy9pS2h5R0taNU9ZSzFJclQ1NUh2OFh1SzJSY1hFNXhYYVZnSHdYTFZSeUoyTWhHYmJKTU9uTFJLVk1ZNHUzTk9iSjRZSnJuSGRRQWQxTm1UQjdMNlVRQzlnZVhhYTFTZXBpS0F3azMrcnkzeTQ0eUFkdmZjZHQxVVloRmI0NkRWUnJnUGJ4ZGk5a0NSa1pwOUx1VHNVMDJWanhTQWFzdDdlbG9WUHRJNnh3K2IxeHdKbXdRclhDNWhJN1hGYk9BYk1ZRnBtSjdJNDBWYVJwRlYraDMxYzBMK3ZpSXpSaDJNMTUiLCJtYWMiOiI3MGQ5MDc2M2UxY2E2ZjNlM2Q3ZWM0NTFhYWY2Y2VjY2RjOGJkYmJhNzIxMjJmOGVhNDYzMDYzNmEwNzVmNDliIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InFmcDl1SEFXdDVwOGI1QWM3Um9SMnc9PSIsInZhbHVlIjoiMCtiNTdKVnRZYnNtL3hoQUpFYU1tcG0zWlNxa2VrdW45d1V3TTNXRFVFOXJKUm1xR1NOMFp1UHVDQ2NpVUZXSG1mZW1Ga2tzQmV4RnhNS3JRVmU4K2RDbzNmSGs4a2FMR2IrdzNhRXZzRUZWYXF2d1dBMWFKNmx1WXJYUXhmS3dNbHZMbmJTMVA5RWF4NGlyeDZTY0FISWZMeXltTU01UUhRbGVhSlhsZ1owRDJkZk5BMU1JVVh4cDVxRXhRZmhRVVpWazFHMEJ3Y2p0ekpGb2FpdG1HNkZsbTFEdXV2aHRYanowOW1TdTBaempLL0puZXFmLzRvYmJZNFVHRE1BYXNmOUtIZFBxWHZOR0JIMy9Sa3htOGJpa2NmbFBTdDl1ZUh4ZTBEVnJwZ3RudGVQWHJuakxQa09QVmpnYlZiQ2xNU1A3Q29rWGNQLzI2dGdKNUpzU3MxR1FNMGJKMGhWdkNRRVZlM2JEZzFSOE5FSDUvcEN2ZVdGN2xJMElGak5KOTNQMWhtMkJmWmV1STFPZ2lka2t6NHBvTU9GZlU2ZE5YSTJHUlZyTGtNT1YxeVBEb2FOb29YaDIxM05waUlGS1JBSFFMWnRFSUM3YmJ4djZwYzh3dHRMSTV1RUx4NDkvV0NsemhwTE56ZWM1Ni9mR3Y0WjBOc0Fkd1hBUHBWTFUiLCJtYWMiOiI4YzFkZmRiY2Q2OTliMWUyNmYyOTdmNThkNGU0YWVlZjlhMWM5M2U1ZjEzMWJiZWMyMWUzMDAyNjNkOTllNmQ1IiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653784620\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}