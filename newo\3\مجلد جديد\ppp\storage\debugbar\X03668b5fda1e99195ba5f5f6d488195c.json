{"__meta": {"id": "X03668b5fda1e99195ba5f5f6d488195c", "datetime": "2025-06-19 12:35:01", "utime": **********.751931, "method": "GET", "uri": "/financial-operations/product-analytics/stagnant-products?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.029151, "end": **********.751954, "duration": 0.7228031158447266, "duration_str": "723ms", "measures": [{"label": "Booting", "start": **********.029151, "relative_start": 0, "end": **********.638551, "relative_end": **********.638551, "duration": 0.6094000339508057, "duration_str": "609ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.638566, "relative_start": 0.6094150543212891, "end": **********.751957, "relative_end": 2.86102294921875e-06, "duration": 0.11339092254638672, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47134184, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/stagnant-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getStagnantProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.stagnant-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=514\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:514-584</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02706, "accumulated_duration_str": "27.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.696176, "duration": 0.02109, "duration_str": "21.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.938}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.731133, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.938, "width_percent": 3.511}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, ps.purchase_price * wp.quantity as stock_value, DATEDIFF(NOW(), ps.updated_at) as days_since_update, `ps`.`expiry_date`, CASE\nWHEN ps.expiry_date IS NOT NULL THEN DATEDIFF(ps.expiry_date, NOW())\nELSE NULL\nEND as days_to_expiry from `product_services` as `ps` inner join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT DISTINCT pp.product_id\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 and `wp`.`quantity` > 0 and `sales`.`product_id` is null order by `stock_value` desc limit 20", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 563}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7363381, "duration": 0.005019999999999999, "duration_str": "5.02ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:563", "source": "app/Http/Controllers/ProductAnalyticsController.php:563", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=563", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "563"}, "connection": "ty", "start_percent": 81.449, "width_percent": 18.551}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/stagnant-products", "status_code": "<pre class=sf-dump id=sf-dump-650670433 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-650670433\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1202032126 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1202032126\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-382470616 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-382470616\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-859619134 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336494796%7C7%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJrSytwbkZtMTRZWWhzQ0p5ZzF2TEE9PSIsInZhbHVlIjoiUUhid3gvSDRhSWpJeUFOUHpTcjQvNHpwWE0rYVJpTmQvQi9CanhGYkFKK3lEdHpUbmNjR3Rpb05sN0JxUVFFQmIvRVljU2ZnOWtWTUFTcUVhdHVGU3oreURTd0tJUVdabXB1QWV5bEpKSTEzVWMyK2ExQ2hsUjFJZHpnTit4OTlRR2xzMzJMTVp4UUdhTk5Kdm4yWThrSjYyWjZRbDh5N1lmMCs1cDlHZWVpOVFDOGJxM3dDNzdTZ0hWYnQzUTR0QUoxZWl6b0ptWWo0VXdodUxhMVRpMnB4OURnVCtzNWxKMENNdFk1R2NZMFB1VTM5U201akFPbE82TUhzamRCaUFTQ0swSDhoV09IcHYweVhEaHR1MzhHU2VBOXAwWisyaFFtY2hLL25YamtQajB4RGxXMjcvR2FPZXVocnFUMHlTdlR5ZzBhd3Q2dnJ3Q1NZN29HbVdyaXJRR2JLeWtqODFCK0hvWE5uRkE3NDNXdWZYOFo5RjdFLzcrbjhQZ3h3VmhEQW15bTVEWFVndlJPQk5JVE5EWkM3TmlPeHJMU3lSYnlrenFuNXI0cWVqcUUvQnhXTStrNzZmR1VzcHgvYkh5TUlmMjBHR3NvdENkQ2E1RWhQZWdHSG9LNGJKeFljUm5uYTFVakhYUVdDRlZHUjV2N1BtSWRiSjhVT25UOTAiLCJtYWMiOiJkOTFhYjIwNTNlMzNiODllOWFiYmYyOTc0M2M4YmQ4MTdhNzYzYTFkMmU5MDk3YmYzNjk1M2VjOGZiYjA4NTk1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpjZUoxY1ZBNGg5dk00Zm9QOWdkWkE9PSIsInZhbHVlIjoiUzlEUGh4K09jNUZpWThIQTNBSUQ4Wml4UkZpaDlWbDRlVWdhRC9ENDA3NzhpdzcwaVVjMy9vVXpyUHI5MUxSdVZsTWVwbTVCMGwvdWtNTVgxblY3Q2UxQmxwektGUlltSVQvRUhFOEdwVStrVUlKQzUxT2xtMWN2RDk2UzByM3JGMnV4NlpNTFVJaUI0aXpNUUxQbXA2VE9RSjB2QUlHaXBqNGVtd09NTFEvandOTHpPdGVZSTYweWZVRkJaMDJwa0NQQ3loMkFUWk9ldEFXSEVReFcyNTFua0ZFL1RheVVxMjlFYXR3TFBORlR2c1JqMGo2T3lxd2g0ZFl6TE1ZSXltb1RLR20vbkNWVXNKb0d4RnB6WGdkOCtBMkhNdE9HdVk3Vy9jcmltOFdFdld0cldnVHpqck1PNGpNYU9oYkpEb3JTOE5Tb1UyRittNktuYnU2dFh0SjAvQTlTMFNBUE9TUXNFcGhBeUZMWHVodUtDNXV6ZkJBbmhGVFdtYVlQbzhUUVVreVgyZ1hsdDhNcDkwcU1sTDVqR3U4bjZTaUI0WWN3eDlZYUFVOVFlcnMzM2t6WG5iNUZVTVNtQk9xVUpiaWJZSTRTNGxYb09mZDd5ZCt5SmlqeTN4aGNlZ2hRZW43RWtRc2EzV3hYVzNTcFpjNnNSTHNTQ1NnZmRXbmQiLCJtYWMiOiJiYzc1Y2U0YTdiMzFiYWIxYzI2ZmQyNGJiMWNlNWY3OWU5OGM1YTI4NDg1NDdkOWQxMzAwMDc5MmE0NDFlZGJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859619134\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-45590413 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BvzDAp8Ceepvt22TODQJA2h22xCzbnkTzOjAbsGW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45590413\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-967381960 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:35:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InB5TDhPTGthRUQvOWJ3MEVma2dNTVE9PSIsInZhbHVlIjoiVFdxL3pTRnN0NUp1bndiYk11bEQwTmxtTWhpaUVWdXNnWTRHVFlGb1JiU0dDZHZBKzhGRS9yYzNoK3RRRVJXYXZBK0thQmFCaEgyc1FLT0FXTzVCeFBWMVZUeTc4aFRJOVBIZHJSMWxHcWR5R04xd2dkMjdycFE1U2ZmNW52NDJ1WTA0SFFwdWV2WEN0OTZ0bjZxUVNRMXZaUmlsMXN0M0k2NVpad1YvcXJJRXFvaWUzTU1VQlhvVGQ4blY1bWl6TjBuV1FTSktoVFhvUWZUWUV1cjMrbUtoN1dvenZFVXlIeVRIc2phTHJkdi81VjV1eVhIcjJ4M1NjUDRQd09Celg5QVFYdTlPcXRsbm1wRms5TkxzNnR2OTMveDlmMlF4R0ZvN0ZHQUxpejJGSWRiOXJuME5vNGtyNG5xTXNrUXljdmRodFZ0TEEzb0EzMVJ6OHFXdFBBNjV6dm90c0k0SG0xZE5MQU54VTVBUG9INExSK1k0NGNlYk9SaW1zRmZTQkV6cUh1M1h1RXVpSkJsbmlzc1J5RzdFWmxuRFVRT1diOUpFL1ZJSHoxTWIvOFdDZGttVFJSR1VRMTBQN2kyeTQyNHhnUzA0SUgrTStCbUEvY3V5clRhQkZGRW1OVTBubk5KakdtR0tvTGY1eVRUZ2l5V01hZ1BhUnpxUGhGVVYiLCJtYWMiOiI3NWQyYTQ5Mjc1MWRmNWNiNzg2ZjhlMmUwOGIzOWFhMzA1YmEyNGNlMmM0NzYzNjYzMzA2ZGJiYjNhNGI2ZjE0IiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:35:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlJoOFFubTQvRUNMZkM5ZVM3Z1UzeEE9PSIsInZhbHVlIjoiZHp2aVI5UUVIN3hLWE16MGVRdnVvSnFLTThTT0ZuNDlQOSt0YWxBa3p6dXBrUTQ1TXQxVmJOU2lya2ZmMzJscTR0T2lmU0JjZUlTb2lyRFRyZEVrQUMwa3JicTBZRG1pK3hCeUx2N2hQZ0JDQzZhWUdBQlBwQlA1ZkF6WC9lSEQrdHNHNEhuZWNpKzBjNHZwVE1Ed2JsRGdIRjR6SXFHSWJ3RHBFREpyZFlsNUZpdWJvaVMxcVNkQlNONXBsY01OcFVVZkFRS3N0VGhjM0dWa2Q2K1J6aENFRGxPNVRDdXRaZXRTSWRTZjNTUEoxVzBjSVMwdlhpcUJVbDc3ODg2SjU1RDhlQnlteit0UE01TFg4VHozN0E2ZkoxN0JQZ205NkgvWVZpNnp1OVl2MkxTOXNldWNtdnUyblhRdnFrZjYwWjRVbEtnVmlZblZRaVJ6bmt4TlNQOGVGNXVxUkNOTzFlVjBaT3lMZUJoUWdsYWpkS2JqL2NNZmFMMFVnZ2QwUEs5UEZybUE0RGxSdlFJcWRjWmdtRzVjMnh1R05WWFFnVVlqbzlvTm1NZ2pHRjRwVFR5bjZLeC9QSVE0QnJaUFhjNGlsVEZMcmZFc1A3aDl3QnJiSkN4K3JsdnJocFlCR2xoMGUralVRaWI2UmE0WTEwQ0xYTVRsaGRoZmxRZU8iLCJtYWMiOiJlZWQ4NzIwZWI5ZDAwYzM3ODViZmRiOTNhMGY0OTNiNDUxYjI2NzkwNjAwMTUwZmQyN2EzNGNmMThkY2RmNjExIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:35:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InB5TDhPTGthRUQvOWJ3MEVma2dNTVE9PSIsInZhbHVlIjoiVFdxL3pTRnN0NUp1bndiYk11bEQwTmxtTWhpaUVWdXNnWTRHVFlGb1JiU0dDZHZBKzhGRS9yYzNoK3RRRVJXYXZBK0thQmFCaEgyc1FLT0FXTzVCeFBWMVZUeTc4aFRJOVBIZHJSMWxHcWR5R04xd2dkMjdycFE1U2ZmNW52NDJ1WTA0SFFwdWV2WEN0OTZ0bjZxUVNRMXZaUmlsMXN0M0k2NVpad1YvcXJJRXFvaWUzTU1VQlhvVGQ4blY1bWl6TjBuV1FTSktoVFhvUWZUWUV1cjMrbUtoN1dvenZFVXlIeVRIc2phTHJkdi81VjV1eVhIcjJ4M1NjUDRQd09Celg5QVFYdTlPcXRsbm1wRms5TkxzNnR2OTMveDlmMlF4R0ZvN0ZHQUxpejJGSWRiOXJuME5vNGtyNG5xTXNrUXljdmRodFZ0TEEzb0EzMVJ6OHFXdFBBNjV6dm90c0k0SG0xZE5MQU54VTVBUG9INExSK1k0NGNlYk9SaW1zRmZTQkV6cUh1M1h1RXVpSkJsbmlzc1J5RzdFWmxuRFVRT1diOUpFL1ZJSHoxTWIvOFdDZGttVFJSR1VRMTBQN2kyeTQyNHhnUzA0SUgrTStCbUEvY3V5clRhQkZGRW1OVTBubk5KakdtR0tvTGY1eVRUZ2l5V01hZ1BhUnpxUGhGVVYiLCJtYWMiOiI3NWQyYTQ5Mjc1MWRmNWNiNzg2ZjhlMmUwOGIzOWFhMzA1YmEyNGNlMmM0NzYzNjYzMzA2ZGJiYjNhNGI2ZjE0IiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:35:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlJoOFFubTQvRUNMZkM5ZVM3Z1UzeEE9PSIsInZhbHVlIjoiZHp2aVI5UUVIN3hLWE16MGVRdnVvSnFLTThTT0ZuNDlQOSt0YWxBa3p6dXBrUTQ1TXQxVmJOU2lya2ZmMzJscTR0T2lmU0JjZUlTb2lyRFRyZEVrQUMwa3JicTBZRG1pK3hCeUx2N2hQZ0JDQzZhWUdBQlBwQlA1ZkF6WC9lSEQrdHNHNEhuZWNpKzBjNHZwVE1Ed2JsRGdIRjR6SXFHSWJ3RHBFREpyZFlsNUZpdWJvaVMxcVNkQlNONXBsY01OcFVVZkFRS3N0VGhjM0dWa2Q2K1J6aENFRGxPNVRDdXRaZXRTSWRTZjNTUEoxVzBjSVMwdlhpcUJVbDc3ODg2SjU1RDhlQnlteit0UE01TFg4VHozN0E2ZkoxN0JQZ205NkgvWVZpNnp1OVl2MkxTOXNldWNtdnUyblhRdnFrZjYwWjRVbEtnVmlZblZRaVJ6bmt4TlNQOGVGNXVxUkNOTzFlVjBaT3lMZUJoUWdsYWpkS2JqL2NNZmFMMFVnZ2QwUEs5UEZybUE0RGxSdlFJcWRjWmdtRzVjMnh1R05WWFFnVVlqbzlvTm1NZ2pHRjRwVFR5bjZLeC9QSVE0QnJaUFhjNGlsVEZMcmZFc1A3aDl3QnJiSkN4K3JsdnJocFlCR2xoMGUralVRaWI2UmE0WTEwQ0xYTVRsaGRoZmxRZU8iLCJtYWMiOiJlZWQ4NzIwZWI5ZDAwYzM3ODViZmRiOTNhMGY0OTNiNDUxYjI2NzkwNjAwMTUwZmQyN2EzNGNmMThkY2RmNjExIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:35:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967381960\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-996914838 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-996914838\", {\"maxDepth\":0})</script>\n"}}