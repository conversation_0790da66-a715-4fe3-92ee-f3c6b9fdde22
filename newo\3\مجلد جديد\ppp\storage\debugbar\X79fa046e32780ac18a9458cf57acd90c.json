{"__meta": {"id": "X79fa046e32780ac18a9458cf57acd90c", "datetime": "2025-06-19 12:34:03", "utime": 1750336443.029548, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750336441.666898, "end": 1750336443.029575, "duration": 1.3626770973205566, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1750336441.666898, "relative_start": 0, "end": **********.889267, "relative_end": **********.889267, "duration": 1.2223689556121826, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.889289, "relative_start": 1.22239089012146, "end": 1750336443.029579, "relative_end": 3.814697265625e-06, "duration": 0.1402900218963623, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46108192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03109, "accumulated_duration_str": "31.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9502351, "duration": 0.02888, "duration_str": "28.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.892}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.997985, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.892, "width_percent": 2.927}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750336443.012141, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.819, "width_percent": 4.181}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1808763842 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336427618%7C3%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhiajdSVUVLa1ZpNFFuTnRyc1dVR0E9PSIsInZhbHVlIjoieHk0UnV4LzRVWjZqaDZjSDFzWFUyWFY3STVBSUpzZ0VWWDhWbkYvbVlWaDZsSzNmRy8yV096c29oNUFxeG16VjhsWG9wV0ZHbHFzcUxOZk5QRlFzU0NGYm9pTU9EbW5IeHd5RVE0TjkxSjl0NktBOUtsYy80RmozQXhuMHU1NE9jQ0M3N1pLa0dwSU01N0oyZW83cm9OT1RabTY2UWd5bi9oSjFaa3ZKWi9SdXlGQVE3a1ZuMHJxbGNwUGZVWGxsTEdhYmtEdGdodThpZFdSQXROY1VzWE5mOTNYWTdGWGZhV0JGaktFQ0xkaCs4R1FEbEFLMGJIRkt1QXplbVVDVXZFL2xPUHJNbDB1MHpSa2dRRTFFWmJKYUdHZDhjeEZXbmlROTFzc0xmTENxWXdRbUw5cnhhL0lzcmxnY2ZCeC9xdlMrbUtqdEp1bi8rR0pOUndaQy9aQlp3UFBxZ0I5SWN2Mk5LRU5rV1ZZbkJzY2N1S29aYmF0TXl2Z2pydjBJNCttUVp1cUtlN2pwRnBvQm95ZmlhbTRob2hsQzlpTmRhL2ovb3VFYVpkRVV4aTdPZEpzOUJQMzVTZXhjSkM5bXhCaGxkamRuRzlCNW1aSkc5N0FuY1hRcDhzb0M1alVlMXBHWmgrVElpK0tGbXdFQXlDY3lnTXNwbXZKRHo3RnoiLCJtYWMiOiI4MTNlMDc5MzU0MTZhYTkwMmUxYzMwNDAwYjg4MjUyM2I4MmI2YzNlYmI4YTQ1ZDdkN2ZlM2MxMjA0ZGE3YjMwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkVlcDRGcmhYSVpxM1d0eDM1aVJ6WVE9PSIsInZhbHVlIjoiZVRhY2M5OEFDNWlYZ3g3c3laQ0tRai82ZEFrTm9RSGFlU3lkWDhhWkFSQ3gvZmQ0R2FNcHN2Sk5wemlQZmlPUU14bkNmTzdvU2dqckR0bDA3TlZsY1UrRGhjRnVJL1o1ZkU4aG1oZ284a0xNVFdoRDNyUGw1WnU4Nm5kcjE3SUdTdkFmcFZYSVptc0RSNDdrYmFSWHpaUjlMRUQ4M1dRK2t4SFJZUWxqS0ZBNi96STZEQjBQREtyVWtGSHJHajhHN1ZOUm9nYTFsVGRzaXJGMmVWNjY5RHl4bC8zQktWZ2ZsNUZMeHhFS0hVQy9vcFNwS0duNnRXTERwemhlYkNDeWRVU2ViZEY3Vk1kWVV2ZFA2ZkFFR0FnTkpraTJNSTV5c0JFWHdra3hPVUc3U25yN0kzWEJweHdQa3psMzl2bGlVLzViNFJHUXFqeFFsYVIrbEFYZkszd1lIRkc3N0daRG1oci80L2hYbGNjTnNyWmV5bjI1aEJaaUlPd1BGakhrcXZLQzhmZktHVmEzKzl4b3FWb1UzYThHRnBrZG5FTDRpNFBTQnRLZEFtZkZsbHpyaXRId3JFZ2krY0cxcEYweWp6N1ZCK3hXWDlRN2ZYbnUxNXBDdjU0RVZvUUNWb1l3b2pncDluSmd1a0VQVE15VW9FaGlzdTVsS3BGRUFHU3EiLCJtYWMiOiIxZTc3NTFmNDJlNjQwMWVhMDI1MzM0N2ZmNGYxZjA1MjQzMTVlZWRlZDhjMWNhMDA0ZTAzZTNiZmRkMmExNTNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1808763842\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1994788669 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BvzDAp8Ceepvt22TODQJA2h22xCzbnkTzOjAbsGW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1994788669\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-255113875 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:34:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ind4SDdsOEtveHdEeW5RWXBsZHBwV2c9PSIsInZhbHVlIjoiTnlGVTM0R2tRRDk1bUNVVFBHMkNndllmT0Z2bEhxY0pzWkRONlNoWGtJTHBCeWFjNHNLWmlxa002TFUyWXNMOERDclBoczZUT1h4bFpGR0NvZGpySlF0WWN4eXhBZ3pKYVhmc2JFZHFPOGV6b2lNalJidjZtUHZDcFhjRmwzQlhocVVaN05DUTNOQTFYZmZtYWJtZ01qUWh1bS9rT0ppaWF3OUVxcG9ETDVHWlc0KzRicnZZNmpLbzBHcW1URDQyMEwxMDBRK3ArSkxZaEpFT1I0MFZOV0FPRVg2QTdWd1Zzb3gxa3VjMzEyWTlURHN5d0ZGOEhjbUZDYmlONldiL1I3T3JwRkROT2JoWUR4eFpKOGUraUQwVlNBekwwYWx6RE9Ta002UFFQTDl3bkk4WDNSd1JxbHduWFRKZVBSbmsrT3o0U3VFTkhZbnBWTXpzT1VDamZBVWdEajFvSjlKVEs1VFFlZGJ6WTFJU3BmYko1amV1QWN0MVJ5UFRuK3Y4a1Ava1U2OUJzWWs0VUFwaGJpSCsyZ2tkWWdsR2swMlBoQXJwSGZBYWtPTHBSWnBNb1ZUcEcwV2FBd3AzdDdhMGk5Zy9MUjlwUmhrWjZURlBrRkNHU0FUbTZnekVCRzFTbTkxblQ3THp6bXVLenZmQzRMU0NsZWR1ZW9jaGtxWnoiLCJtYWMiOiI4Zjc0M2QxN2I4NTA1ZTdiODk1MWRhZTg3YmU2OGI3ZWE2ZDM5NDg3NDMzZjlhY2VmODQxYjUwMDM1OTc4MDhmIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:34:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRCUko0aDRyaU16UEJpUy9sT0FUbUE9PSIsInZhbHVlIjoicGltb3VlOUZSenNaU2VKcWR5YXdtN0pjamxwN0tBTk9HT2pUWXZZSWJJdjRhNDMzNTNmYkszc3d1RjhJNlRDNXRXLzk4QkNzelFHdnlXbDZPcGNtZ21wYjFHM3dPYWx6VlZHR2lXK2xSMm9tNDlPeW9IeFV6MHYyZ0lZRjE3QSttSGJVVGhoT1hNYkVudXBDQm53RWtWNUd2bzhKM1lpN05zVzJ3LzNwaUNrRkdSK0NGb2dtT2crZVpkK3QxSWszQXV2VEtoOTdZbVJQNGNOeW8yZURUUXlSWmdSMnd1Z2g5TjBXNlVKam9RRFdvSFZHR3J5WXVMdVFxdGpjYlh2V0tDWmZrQm1sV0tuQ25oU1FJbUUvMXU1YmJzWVd6ak80RG5nT2xyaW1kVGFtYkZqT0NCU2FGVEU5UGV2bWV0UmtPVTNOZTFGZUR2T0x2UDFiRityVGpZNUJBeXJOekJkRlpJKzB2ZXR6LzAxZ3pxVWlPMnVsc3BGUDV5c04wN3FZcGpHUUNJKzlGNVJoWDJKTWxVTlhVcUFoTGloNGM3VzRsZGo3L2M5M0ZkWHdVYU13eXVydXZFMHdDTjUwVmJNN0xESW5WZzYxa3hXSkNqSTBBR016b1VzV2R4dmVKM1g3QURmOEFWbTByUzhQVzIrSk13bG1LQkhyc0Q3OXNCT3ciLCJtYWMiOiJhYzBkZDE4YTI3Y2YwYTA2MzljNzQzNjZkMjc2ZjVlY2VhOWY3YWE2NDk0NTk1YzIyNTllZTgxZmViNmVjZDAwIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:34:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ind4SDdsOEtveHdEeW5RWXBsZHBwV2c9PSIsInZhbHVlIjoiTnlGVTM0R2tRRDk1bUNVVFBHMkNndllmT0Z2bEhxY0pzWkRONlNoWGtJTHBCeWFjNHNLWmlxa002TFUyWXNMOERDclBoczZUT1h4bFpGR0NvZGpySlF0WWN4eXhBZ3pKYVhmc2JFZHFPOGV6b2lNalJidjZtUHZDcFhjRmwzQlhocVVaN05DUTNOQTFYZmZtYWJtZ01qUWh1bS9rT0ppaWF3OUVxcG9ETDVHWlc0KzRicnZZNmpLbzBHcW1URDQyMEwxMDBRK3ArSkxZaEpFT1I0MFZOV0FPRVg2QTdWd1Zzb3gxa3VjMzEyWTlURHN5d0ZGOEhjbUZDYmlONldiL1I3T3JwRkROT2JoWUR4eFpKOGUraUQwVlNBekwwYWx6RE9Ta002UFFQTDl3bkk4WDNSd1JxbHduWFRKZVBSbmsrT3o0U3VFTkhZbnBWTXpzT1VDamZBVWdEajFvSjlKVEs1VFFlZGJ6WTFJU3BmYko1amV1QWN0MVJ5UFRuK3Y4a1Ava1U2OUJzWWs0VUFwaGJpSCsyZ2tkWWdsR2swMlBoQXJwSGZBYWtPTHBSWnBNb1ZUcEcwV2FBd3AzdDdhMGk5Zy9MUjlwUmhrWjZURlBrRkNHU0FUbTZnekVCRzFTbTkxblQ3THp6bXVLenZmQzRMU0NsZWR1ZW9jaGtxWnoiLCJtYWMiOiI4Zjc0M2QxN2I4NTA1ZTdiODk1MWRhZTg3YmU2OGI3ZWE2ZDM5NDg3NDMzZjlhY2VmODQxYjUwMDM1OTc4MDhmIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:34:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRCUko0aDRyaU16UEJpUy9sT0FUbUE9PSIsInZhbHVlIjoicGltb3VlOUZSenNaU2VKcWR5YXdtN0pjamxwN0tBTk9HT2pUWXZZSWJJdjRhNDMzNTNmYkszc3d1RjhJNlRDNXRXLzk4QkNzelFHdnlXbDZPcGNtZ21wYjFHM3dPYWx6VlZHR2lXK2xSMm9tNDlPeW9IeFV6MHYyZ0lZRjE3QSttSGJVVGhoT1hNYkVudXBDQm53RWtWNUd2bzhKM1lpN05zVzJ3LzNwaUNrRkdSK0NGb2dtT2crZVpkK3QxSWszQXV2VEtoOTdZbVJQNGNOeW8yZURUUXlSWmdSMnd1Z2g5TjBXNlVKam9RRFdvSFZHR3J5WXVMdVFxdGpjYlh2V0tDWmZrQm1sV0tuQ25oU1FJbUUvMXU1YmJzWVd6ak80RG5nT2xyaW1kVGFtYkZqT0NCU2FGVEU5UGV2bWV0UmtPVTNOZTFGZUR2T0x2UDFiRityVGpZNUJBeXJOekJkRlpJKzB2ZXR6LzAxZ3pxVWlPMnVsc3BGUDV5c04wN3FZcGpHUUNJKzlGNVJoWDJKTWxVTlhVcUFoTGloNGM3VzRsZGo3L2M5M0ZkWHdVYU13eXVydXZFMHdDTjUwVmJNN0xESW5WZzYxa3hXSkNqSTBBR016b1VzV2R4dmVKM1g3QURmOEFWbTByUzhQVzIrSk13bG1LQkhyc0Q3OXNCT3ciLCJtYWMiOiJhYzBkZDE4YTI3Y2YwYTA2MzljNzQzNjZkMjc2ZjVlY2VhOWY3YWE2NDk0NTk1YzIyNTllZTgxZmViNmVjZDAwIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:34:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-255113875\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}