{"__meta": {"id": "Xe3aab90c3322ad227a6e2950e9c6d6c9", "datetime": "2025-06-19 12:33:47", "utime": **********.557403, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750336426.614519, "end": **********.55743, "duration": 0.9429111480712891, "duration_str": "943ms", "measures": [{"label": "Booting", "start": 1750336426.614519, "relative_start": 0, "end": **********.413293, "relative_end": **********.413293, "duration": 0.798774003982544, "duration_str": "799ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.413367, "relative_start": 0.7988481521606445, "end": **********.557433, "relative_end": 2.86102294921875e-06, "duration": 0.14406585693359375, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46165200, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02026, "accumulated_duration_str": "20.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.468773, "duration": 0.01569, "duration_str": "15.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.443}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5042322, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.443, "width_percent": 6.614}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.525211, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 84.057, "width_percent": 9.378}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5403912, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.435, "width_percent": 6.565}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1842285594 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1842285594\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1229621483 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1229621483\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-610246990 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610246990\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1601234354 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336418704%7C2%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFGMnNDbjF6eWhoQ21tNzRESjJBSFE9PSIsInZhbHVlIjoiczNLYUp0YVR0aUhGTnc1dGFJeWFwd3VwODJWWEhxWGZFMzJFTVh3UE40NERxenhyeituVDNxdVZWRXBtMHJZVm5kSE1taWMyYjlhMGUyb3BqOUU3OGd0L1JNQzhuSWtsMW56ZTh0TGFidlJzOFJQditpL3lwS3JWaDVSVTE5dlN0UzZZcGdYTDBraTE3S050clBmbjZIcXRVS3JiRFlQb0RwWTF6eHJmOFJ0a1FmbW53eWs0RE9abFAvRGRkTWQxUWRKVEcxZWNCVXlJdE5ETjVoN0tzbXRCUEVBS3lUdjZyV0hiZ3F4MjI3bGV3WENKZXRYVEFmb1JXa3p2ajNyWHc1ayt6L3lhKzRiSmhLOFdUa215UFQwYTJCRHkvTHhYeW01NU9qWXlFMlFadG15ZE9DNkwrd1ErYnE5TDRHR2pQTW51NTkrenl5byt2SWgrbnRsQTlnekRGWGlnamZEeWdETy81MVpRcnBzTVJ5aUt1b3dMSDEvK3VVTTVDNTFQVUNmL1NCU3dyL1lDL244M0I3eU9idTljRmJ1aVhRY2llN2wySkZIZXRCVjM3Ui96Yk5rTkV0ZTRWM3RDSTVzQVFiVytDNXNwbUt2dFJWUDRmanEwQ1JZVTg2ZlZXZzlVMGhDRUYxRlRXWjVCQWM0cHc2M2JhMnhMbmxtOXkvNUUiLCJtYWMiOiIyODg5NDkxYWI5YTUyYzY2YmMwODgyYWRmNTY4NzU1OGVlOGUxNzkxNzJkYTgwNThiZDM1MTUxYmU3ZDM0YTJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhYYmVzcG1Gc25hOENneXNwa1hWWEE9PSIsInZhbHVlIjoiTnZLeHowUkl5NEIxNXhaNHQ2REZWQm40QXVPeTJucy82ZTVoeVkvV2tvS1h0WlJUcElTNDRHQnZsdnJWNFQ3cGJLaXNCWFRMK0lLdjdtVFZXMGhUOTVlaWM0N1A3L1M3TUJVK3lmazc3N3MxRUJ5Qzk2MVhXUW84eVAzeVhhRUJiVjV6TEJmUVRYbjloT2ZUeTdSMHcwSW91dm8yVUJnek03TkdZYnh3Y0pzMHlJUVlPM3B4WGtJRG90VXluVzNKWVBNdnlEK2pIMXE3a2tCNjFUbWtVdXBzbnJSSDBZNWxuRGV2NTd2aDBIVzVsMHZFUEgvaGh2UFRoQ2RoWFR4aWwwVkxBemlpeVM1S1l1NEU3c0hOMUtlMTFBa1VTcVkxSW4yUllBZEdRT3BzY05nM0hCSXQ1MGh4SGNXdzU3MnhtOFdKUUJKTDFUZ3FjeHE0NXo0S1BwNVE4ZEt6dkxtUUJSODFyRm5Md2R2SWFLc0ZSN3Uwc2ZOZ1I3OUdNMmJaeUQwcitkdzhoRVU5ekg1ZWtGOXBZRDczU3NUalozMjc1Y2VLUFFxRTIyd1kyQ2puVGwxS2NYNWlZaGM3RWdUNXVoWENReUtRNTQ2a1o5UlZ6ZGNFSEtGbVVFektLTWZnY1YrNjRVczM1dHcwS3NpYTRsZzl6bC94eVIvTTVhTnUiLCJtYWMiOiI3NjI5OTgxMTFiYzgyMTg2YTcxYTM2ZDE2MTcxOTVmMzg3YjNiZDczZjBiYjVhY2I0YjQzYjM0OTdmMGRiMGZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1601234354\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-162344490 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lYCgmFS8fqkPjKv2BfRzZo9jiALUMXID3HgtHMZN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-162344490\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-265714729 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:33:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpTODBOdkNqc0NFMkRwMmJRb3VJd2c9PSIsInZhbHVlIjoidnpvdFVpLzJpcXovQzFLeWgzcFVzY3pUOExXQ0hOdkdzZ0VZVjJ2cXJxbnUxZDRQRmt5VFFoMVR1VURpeEtnTG1qNmZDN3dIMVE5SVl3blFLYnpsYW1FR2hYdlNRMFNFd3ErMVcrVUdzeWdkUzJmWUlRYmFPcDRIdnp2ZnYxeExxa0ZJOGJWTGFOZHdXNDN3cjh2clhOWGRLeXpWTUtFOVBvakdwYzVKK3E1MXdHWGtEVXlXQzhYMzAvbFdQT3l3WlRxRnYySXI0VmNmSTIvVXlVTUtnNlh4UUFLWGtFUjhRbzV5SExNWnRDUzJ4N0VIZWFjdzRwOVZOMTgwZFpBYnVjQ2xQT1hpMm9ySUNobHRDU3FPK1JKLzhnZzRUZWlHMWw5KzJPbVZhU2NKVWozakFGZEUvMVpNdm1rTDgzUDlkczltUUE4RjVkcEVRUzZQY04xMCswbDN1VHpEZURyU2lNY1JUTnJaYlR6b0Mvakx4UTdUZSt1YUxUN1Y2Y2x1d1Y2MEdkQ2VyNWdSTmV5YVZLcml5QlZxUU5XRTNITmNYK2R0N1NVMkpvV3czejF2ZUlxKy95RmlLSUI2OEw2RUZQclpiUUxodnFvN2thSTIveklUWHdBZW0xeGVIMmhEeTFESUFaa04vSnBkVVNxb2hCbElKMnZ6VEJFTjJuR2ciLCJtYWMiOiI4YjAwZTBkMTUxNmU5MWVmMWI3NWZhZDFkN2Q2MWZkYmY5ODdiMDkzZDU4MTg5ODcwNzhkMmZjMjVlM2VkNGFmIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZCSTBMbXdVWHN2RnF0S2pEakJweUE9PSIsInZhbHVlIjoibFk2aXQ3dGtKdnFPa084c0w4VUI2NHBuQ2pLdTRmVlQ2VUd1aVhSenNWMjBlR0MyN21pMlBZMENES2hmaVBpdFpSc2tNVEZtSHJMa2xIMWMxK2xlcE9tRm9BWFlZU2VHT2dCeWJTcW1ZL1d3ZDFSRVhORG1iRDRmblMxeW4yY0lMVkZKdWxXNEpMdVFLQllCSVNBQWdUa1hzU1VvYldFUFJOYjYrdkF0Z2FRWW12M1BFZjJla0syVkJoak16V0Z0OTlSbWIyNHpGUGF3dThWcEY1RjlqZDJKejM3bjFnYXJVd0ZrY3JONVl2OHZjY05XcTIybXFSekpDKzg5ZjJEU2NHU1Q3TVJnNFBvMGFabzVQNWZ2MENTTFp5Z0lLckN6QldkeGp3WUkxQ1lIQk9FUUk5TjlMeEtTUzNEYzlOa0dTKytScThiOGc0U3ZuQUNnZkFzZW85c1dpN1Z2aG5WUm5TNFRaV2taNzhLWDZqUnlEUitxMXJ3UFNMRTk2c1c2QXJEK1FJT0s0c2dONFRtMzd6Ny96TjNrZS82TnZWUHJJTWJKcnVxeFE1dVlHeHhiWDUxVGJDZmJ0K3NjMXRyV0FGa2thUlN4bjUyVFhkMUNHRElsUzVFWHVSN0wwWEdYL0czSk8wQ0RwcUtlYXNOdHM3MlN0V0JueGcwdXA2b3MiLCJtYWMiOiIwNTM4Y2Q4OGU5ZTUyYmRjMWNhZWFkNzk4NDQyODQzN2NkNGMzOGY0OGU1ZDBhZWEyZTg4ODZhYWQwMjgxNGFkIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpTODBOdkNqc0NFMkRwMmJRb3VJd2c9PSIsInZhbHVlIjoidnpvdFVpLzJpcXovQzFLeWgzcFVzY3pUOExXQ0hOdkdzZ0VZVjJ2cXJxbnUxZDRQRmt5VFFoMVR1VURpeEtnTG1qNmZDN3dIMVE5SVl3blFLYnpsYW1FR2hYdlNRMFNFd3ErMVcrVUdzeWdkUzJmWUlRYmFPcDRIdnp2ZnYxeExxa0ZJOGJWTGFOZHdXNDN3cjh2clhOWGRLeXpWTUtFOVBvakdwYzVKK3E1MXdHWGtEVXlXQzhYMzAvbFdQT3l3WlRxRnYySXI0VmNmSTIvVXlVTUtnNlh4UUFLWGtFUjhRbzV5SExNWnRDUzJ4N0VIZWFjdzRwOVZOMTgwZFpBYnVjQ2xQT1hpMm9ySUNobHRDU3FPK1JKLzhnZzRUZWlHMWw5KzJPbVZhU2NKVWozakFGZEUvMVpNdm1rTDgzUDlkczltUUE4RjVkcEVRUzZQY04xMCswbDN1VHpEZURyU2lNY1JUTnJaYlR6b0Mvakx4UTdUZSt1YUxUN1Y2Y2x1d1Y2MEdkQ2VyNWdSTmV5YVZLcml5QlZxUU5XRTNITmNYK2R0N1NVMkpvV3czejF2ZUlxKy95RmlLSUI2OEw2RUZQclpiUUxodnFvN2thSTIveklUWHdBZW0xeGVIMmhEeTFESUFaa04vSnBkVVNxb2hCbElKMnZ6VEJFTjJuR2ciLCJtYWMiOiI4YjAwZTBkMTUxNmU5MWVmMWI3NWZhZDFkN2Q2MWZkYmY5ODdiMDkzZDU4MTg5ODcwNzhkMmZjMjVlM2VkNGFmIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZCSTBMbXdVWHN2RnF0S2pEakJweUE9PSIsInZhbHVlIjoibFk2aXQ3dGtKdnFPa084c0w4VUI2NHBuQ2pLdTRmVlQ2VUd1aVhSenNWMjBlR0MyN21pMlBZMENES2hmaVBpdFpSc2tNVEZtSHJMa2xIMWMxK2xlcE9tRm9BWFlZU2VHT2dCeWJTcW1ZL1d3ZDFSRVhORG1iRDRmblMxeW4yY0lMVkZKdWxXNEpMdVFLQllCSVNBQWdUa1hzU1VvYldFUFJOYjYrdkF0Z2FRWW12M1BFZjJla0syVkJoak16V0Z0OTlSbWIyNHpGUGF3dThWcEY1RjlqZDJKejM3bjFnYXJVd0ZrY3JONVl2OHZjY05XcTIybXFSekpDKzg5ZjJEU2NHU1Q3TVJnNFBvMGFabzVQNWZ2MENTTFp5Z0lLckN6QldkeGp3WUkxQ1lIQk9FUUk5TjlMeEtTUzNEYzlOa0dTKytScThiOGc0U3ZuQUNnZkFzZW85c1dpN1Z2aG5WUm5TNFRaV2taNzhLWDZqUnlEUitxMXJ3UFNMRTk2c1c2QXJEK1FJT0s0c2dONFRtMzd6Ny96TjNrZS82TnZWUHJJTWJKcnVxeFE1dVlHeHhiWDUxVGJDZmJ0K3NjMXRyV0FGa2thUlN4bjUyVFhkMUNHRElsUzVFWHVSN0wwWEdYL0czSk8wQ0RwcUtlYXNOdHM3MlN0V0JueGcwdXA2b3MiLCJtYWMiOiIwNTM4Y2Q4OGU5ZTUyYmRjMWNhZWFkNzk4NDQyODQzN2NkNGMzOGY0OGU1ZDBhZWEyZTg4ODZhYWQwMjgxNGFkIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265714729\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1174091883 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1174091883\", {\"maxDepth\":0})</script>\n"}}