{"__meta": {"id": "Xa39a02a50703b6dc1fe9bc1e77f95bc0", "datetime": "2025-06-19 12:35:04", "utime": **********.173278, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750336503.352894, "end": **********.173305, "duration": 0.820410966873169, "duration_str": "820ms", "measures": [{"label": "Booting", "start": 1750336503.352894, "relative_start": 0, "end": **********.034707, "relative_end": **********.034707, "duration": 0.6818130016326904, "duration_str": "682ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.034725, "relative_start": 0.681830883026123, "end": **********.173308, "relative_end": 2.86102294921875e-06, "duration": 0.13858294486999512, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47118664, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-509</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0307, "accumulated_duration_str": "30.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.107373, "duration": 0.01789, "duration_str": "17.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 58.274}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1411352, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 58.274, "width_percent": 3.746}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 order by `turnover_ratio` desc limit 20", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 488}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.148634, "duration": 0.01166, "duration_str": "11.66ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:488", "source": "app/Http/Controllers/ProductAnalyticsController.php:488", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=488", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "488"}, "connection": "ty", "start_percent": 62.02, "width_percent": 37.98}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-892980079 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-892980079\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-168738606 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-168738606\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-69065168 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-69065168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336494796%7C7%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpvNVVySWFNQ1FCWEhMMEZXTEpCdUE9PSIsInZhbHVlIjoiSExqamhOUElEdVJ6WDBXNmVjeXEwdk9kU1RTYzAwZW9kVDdaK1BiNjltMTBSazRNWUhXRTdja0dzeXVHeVB2M2JONXFiNkw5TjIvcGtuTXYxWHBnRVE0NllyWWhqUCtYcU45OFhlZ25LNnR6cFZhWCtqbERmdGRCRjRvdnRMR2V4Z2E1ajl1cStTeTZPWkVGQzZUYXJ1dHhpTk1HOTY5L3ZKRjUvZlVpWUI4VGhJcjZscnJWdUdJM3NJWTc2UnV1OXlhOGt6T2ViMjhWa3hTbzJsV2ExUUNIMkVrcVcwYXBZajhSOHd6ZVBUY0liZlhFNE5XeWdsT0d6dnJLOVRrZFpXV3JaSG16dHA1MUQ2NldLQlpsMGNUSC8yV3d6R3Exc0tjQnpwSmpVdm5pdUh0SmlQemI2d05ROU5mYWkrclB6QkdlaWREZ2l6Z2NKa2dpRWVjOVBYVVpJSlpOL2t5b3pOYzlzSThoWXUxazhBWlJYLzZhOE1QUnRhQWlrMTdVUHFML2lkTGJtVHUrSC9lcXlSTEl1UlR0YU1MYXdGZHZGTTE4cGZGdDUxU1JyMG0ya01WOFdiQVFmN2FtQUl0VmlrVjFtaUtpTzdHdncvZG05dExtckkrKzZJSG02S0t6ZVVUcm9xMFdrdHRUMThwL0dNVlpCR0R5eVlDTit2L2ciLCJtYWMiOiI3YWQ2Mzc4NGRlNjhkN2I4OTA3NTIwODFlZmM2NWVjNmYzN2M0MmYwYjYwMmEyNzk1ODAzYzU4ODY0ZWM3N2MzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFoNldYUlh5b0NhSTczaFcwMk5tRWc9PSIsInZhbHVlIjoiemxMUjZiUXVMRE96RE1PdkZnZ3NvMEtUUXlzeFl5QktMYWJBL2tVV3E0Ry9HYS9YZENycHpYNEpwR3prVGQyOUcvQ1dpZDlkMFRvVzRDeHk5VXVnQmpGT1drM09zVS9CMTAzREo2eG0vYkJYeDRSMk52bUpDRjRqUXJBUXVGelJyOXZuWDNrRUsyY2J5ek01U1FqMEt0UEJaZzA0ZllwbXFiQ1hOQjBicjY2MzhQdDUzSGJEOTd1a25DY2pRY3prOFBhSUE5Tmk1bGdMSzRRN09BRDZ3N0l4Q3B1YjhETldtbjg0a2FSb09ack5vaVUvZEdub1FtTFRzejdkdFRDQkE0MWNYQU5jcWxTR0g1cnRzK0F3VXdtbHI2STVFWEdwU00rWmNKcUtuL1phRm5HUmxSZnFQTHp0WEhZTWtoUkRqN2FRbkNYWkZpelBlUXcvajh2U1NtK0RNZkZRbWxKU01XaFo4ZXYvWjkvQytJL3NsbzM1MWJnMDMwZlByL0lzNEM0N1NudjR0cVdnYUtjMXFTeEJBTmwwb2JQalpCRXBLdjNGdUNmaUM0QnY3V3BibUNXR2Q3bjRXT3VKL1AwaEE2MDcvZkVkYnBZZFY3c0hYM2JKMmdQdndyTG0ySmd0KzNoM3A2a0lvZkpra3NqZWI2WnZFNnJLMDhnUlZMcWQiLCJtYWMiOiI1MDg5ODgzNzJlNjhlNGRjNTgxMTAzODdiYjIyOGI3ZDVhZTBkNjY3NDViYmQ3MTcwNGFmNzVkZWJiYTQ3MjJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-846813706 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BvzDAp8Ceepvt22TODQJA2h22xCzbnkTzOjAbsGW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-846813706\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1981073639 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:35:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktncTdaaVNRSEIzVnMzYVlNVTJvY2c9PSIsInZhbHVlIjoiVlFzMlRrUTNSSVpMMG4vTTVkMzNMQmJabk51STBCOStKMUN2Ym1Ya3k2UnpDNStmd3l1QS9HYW9DRE1VZmJhS25FYjVCdDhOSUFaZVZ6RjYxMTJkUUxFaUZOZUcrMG9tbHF3bUhMaGZVS29XeVFkckFZNk8rbDBpT1FpdFp4NGtLUnFjNWZNV0pzTWNQdE1NZ05sK0J5V2k2KytNcHRmdE13K0Z1cHFQSXFlclphZWQyRng0VVJLR0tBQStreTQ3b0FmUkl4WlcxMG9EMThZZndwbzVZVmJZM2ZMSElzRGRyOTRjN001bElLdFlmRkhWS0RINnlFL1ZId1NFbGMxUXdXSXc2Y1h5eFFUMGhEODhhV0swMUlyano3YUY3OCs5ZVl0ZUtMOStPbXZQd0lhbC9leFJiaUtGOVZnK2ordjQvcTlhMEFhWFZLbFZESFNWZmdTZHRiQjVhUFFOcTZJVUZ4S0VwVUhhUitqM3AzZnlOclBYZ2JQWU5Nd1p3S2xyUUpNTG5wd2JTZHRnaEd4SlZ6dW9na09kUW03ZWpGTnJXbnM0SG1RcGMxcTBEalV1bFRXVkdiM3NIMXlMemhPdk9BbXI3UG5GbW1KYlhjN2RvNFI1cVB6U3JUV0tWYlhadGZ5YTRDQU1mQi9pNmFVL1FzNDRueHdYZzFRWjJWbmkiLCJtYWMiOiI0NjlhYTRhZDUyNGY5YzUxZWJhZDRhNDRlMDA3NzY0NzAzMWY1ZDY5ZTliYjc4ZDE3NTgwODQyYzUxYWNhNDViIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:35:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9PWVllRW5kdWZSTEZUZmZxc3B2c2c9PSIsInZhbHVlIjoiemhUTmhuV2svRlBnNWRHNWhVN09xUHp0TWp6L3IrYjhEcmZUTjN1MEFEODd0OE1waTR1emVsKzZLVy9GY3lMc1FEZmRNaURXcUo2R243MXo1YUc2cUZRMlZwUi96dUdsUVJwOExFbnBwNWxuUWlZekdmMG9ZdGFtOEFzYXRaNkRsNVJWek9nV1phZ2d6TE9ndnUvZ2QrbnY2cm51S2ZHZjA3OEJXcnFtdHJKeXgwejFHSXR1S2h5ZTh2SHlDTTRZd3Jpa21BQlQvZkYvQXdMYVhIZjh2empvV2c0WkpKSVBjWG9qalBtS0VHNGkyZGowYVVxNldqYlo3dFNHZE9IT3JpTFcwUE9MRW5BaTRXUllleXdSUmNZUmp0YUZrV1FVQ2VxVmJUK09UL0JTcERXV2ZZelc5bmhheUJkZWYwY09ra0k4OWIrM2xwclJWNG1KbmVpUFhXMXh0L2JRcmZycStSaTNvNk92dFNZOU5DRE84WHp3eGVabDZMWEtvd0dCZXVzTnZFclJadDFidys1bnZJRGFVb3VFNGhTbkI2VzBhMmJDOWZ1T01FNUlnSG1mbEhzV00rcDJXOHhoK2JGSFdLTkdTcmxkNjlWNEJROGxPQVlZWUUxc1EvWjhTZmN4N1EwU1F0cTR3M1kxcjF0RXNpNkYySEVyYVhXbTZ5MloiLCJtYWMiOiI0NTE3Y2FhOGRmM2UyODE4YmExZWU2ODA2ZmM1NjM0ZjAxM2FmNTRiYWYzMzZkMWFiNTNkNjhmY2QzZWU0N2I4IiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:35:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktncTdaaVNRSEIzVnMzYVlNVTJvY2c9PSIsInZhbHVlIjoiVlFzMlRrUTNSSVpMMG4vTTVkMzNMQmJabk51STBCOStKMUN2Ym1Ya3k2UnpDNStmd3l1QS9HYW9DRE1VZmJhS25FYjVCdDhOSUFaZVZ6RjYxMTJkUUxFaUZOZUcrMG9tbHF3bUhMaGZVS29XeVFkckFZNk8rbDBpT1FpdFp4NGtLUnFjNWZNV0pzTWNQdE1NZ05sK0J5V2k2KytNcHRmdE13K0Z1cHFQSXFlclphZWQyRng0VVJLR0tBQStreTQ3b0FmUkl4WlcxMG9EMThZZndwbzVZVmJZM2ZMSElzRGRyOTRjN001bElLdFlmRkhWS0RINnlFL1ZId1NFbGMxUXdXSXc2Y1h5eFFUMGhEODhhV0swMUlyano3YUY3OCs5ZVl0ZUtMOStPbXZQd0lhbC9leFJiaUtGOVZnK2ordjQvcTlhMEFhWFZLbFZESFNWZmdTZHRiQjVhUFFOcTZJVUZ4S0VwVUhhUitqM3AzZnlOclBYZ2JQWU5Nd1p3S2xyUUpNTG5wd2JTZHRnaEd4SlZ6dW9na09kUW03ZWpGTnJXbnM0SG1RcGMxcTBEalV1bFRXVkdiM3NIMXlMemhPdk9BbXI3UG5GbW1KYlhjN2RvNFI1cVB6U3JUV0tWYlhadGZ5YTRDQU1mQi9pNmFVL1FzNDRueHdYZzFRWjJWbmkiLCJtYWMiOiI0NjlhYTRhZDUyNGY5YzUxZWJhZDRhNDRlMDA3NzY0NzAzMWY1ZDY5ZTliYjc4ZDE3NTgwODQyYzUxYWNhNDViIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:35:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9PWVllRW5kdWZSTEZUZmZxc3B2c2c9PSIsInZhbHVlIjoiemhUTmhuV2svRlBnNWRHNWhVN09xUHp0TWp6L3IrYjhEcmZUTjN1MEFEODd0OE1waTR1emVsKzZLVy9GY3lMc1FEZmRNaURXcUo2R243MXo1YUc2cUZRMlZwUi96dUdsUVJwOExFbnBwNWxuUWlZekdmMG9ZdGFtOEFzYXRaNkRsNVJWek9nV1phZ2d6TE9ndnUvZ2QrbnY2cm51S2ZHZjA3OEJXcnFtdHJKeXgwejFHSXR1S2h5ZTh2SHlDTTRZd3Jpa21BQlQvZkYvQXdMYVhIZjh2empvV2c0WkpKSVBjWG9qalBtS0VHNGkyZGowYVVxNldqYlo3dFNHZE9IT3JpTFcwUE9MRW5BaTRXUllleXdSUmNZUmp0YUZrV1FVQ2VxVmJUK09UL0JTcERXV2ZZelc5bmhheUJkZWYwY09ra0k4OWIrM2xwclJWNG1KbmVpUFhXMXh0L2JRcmZycStSaTNvNk92dFNZOU5DRE84WHp3eGVabDZMWEtvd0dCZXVzTnZFclJadDFidys1bnZJRGFVb3VFNGhTbkI2VzBhMmJDOWZ1T01FNUlnSG1mbEhzV00rcDJXOHhoK2JGSFdLTkdTcmxkNjlWNEJROGxPQVlZWUUxc1EvWjhTZmN4N1EwU1F0cTR3M1kxcjF0RXNpNkYySEVyYVhXbTZ5MloiLCJtYWMiOiI0NTE3Y2FhOGRmM2UyODE4YmExZWU2ODA2ZmM1NjM0ZjAxM2FmNTRiYWYzMzZkMWFiNTNkNjhmY2QzZWU0N2I4IiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:35:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1981073639\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2089112259 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2089112259\", {\"maxDepth\":0})</script>\n"}}