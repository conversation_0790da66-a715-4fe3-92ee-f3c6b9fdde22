{"__meta": {"id": "X55e2687671c2e1d525328da01652ae92", "datetime": "2025-06-19 12:33:41", "utime": **********.304915, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.465466, "end": **********.30494, "duration": 0.****************, "duration_str": "839ms", "measures": [{"label": "Booting", "start": **********.465466, "relative_start": 0, "end": **********.177294, "relative_end": **********.177294, "duration": 0.****************, "duration_str": "712ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.177309, "relative_start": 0.****************, "end": **********.304943, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025480000000000003, "accumulated_duration_str": "25.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2337809, "duration": 0.022850000000000002, "duration_str": "22.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.678}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.273379, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.678, "width_percent": 4.867}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.291094, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 94.545, "width_percent": 5.455}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336418704%7C2%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlnWmR2eEV0NnpnMzN6RDFGYnZkR0E9PSIsInZhbHVlIjoiZGZBYmVBV29VemFOU2YrM0VQOFVaUmlWVHk1VW5ZalMwcjErOGdVRUpOQnVJL2wvZTkwQlpYN01IUGhYYnZEaDJDb1k5SkFnbEJDR1crMGVIbXVkYTV4NVZUS3dDRTNnYXMvWXovWkxxcnpnQU9BOTZOa0NyUlR0aFY3Y3NreHorZ2NCNVdRcm0xdFFhaUVZajV0M2VBSitnNk1VeDFJU2lRdE1FNVBKYUEzUlo5RVdhbTMwcjRhU3RBTXFmVVA0aG11c240OFVsQm1BRVBXMW1jeGFsR2R3VTVtcVM5cHlTRmVTVVA1dnJ3LzBBdkVMdDFsUmthNGhpSFJHVHIyT2xRNG5sNGRxcmQ4SWlCcno5anc4elc4a0l5aExFbkJoV3g0QnQvYXcxcTlLTEdnTXlhTHdZV1FxWCtLN0Q0bnFXVFkyWW9LZUVsVjJ2RmRzQlVsM0x5MkVvdm1McExHWVVDcldsdmZta1lDM0pCLzdhdHAxM3hEU3phV0ZBRGlWcVFVODlLbU8zd0VkTGVDaGFJQnk0UUhaV0swai9FQ2t3OGsvL1FMWVBvb3diZDF0T1AyRUhGcStRQmRVVWtsV1M0SHlKMUJraGpOQjREcWFKY29Da0RRZldLSjR1cDE5NmVJQytKbDlwRmpySDB3dUhGZjJiVVdSVUMzVnlmRmciLCJtYWMiOiIzM2ZhMjQyZDYzMmI1NmY1NjcxNTFjMWNmY2M2NDk1NTNjN2E5NGJlOTk4ZDk1MmNlNmNiZjNjODViNjI0ZmJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdKYU50S2JJRU5DS1ZvQVRpM3BTbXc9PSIsInZhbHVlIjoiOFJ3R0xLMzdPYzc5UFZ4d28zMHF5cUFrS2I3REZ3M0pUM3RVV3RZUzcxNXVYYUN6cEoxbU4vT21WZU5keEFzTWFwZ1hhTkQrdmxsQ1RtZ2RlUnA4OW9namdCMEhPR2Frc1RzYXN1aXFZOHREdzhHdzhWWDdGbTQ0d05qSW03UUF4V2FqMDlSNFFQVzdpV3o4VldOd1RRU05wdGRLZ3VINDZkVzBrN0MvQ2pKdi91OUZoeFp1bDRrYnJJNVh6ZWhrTS9XYmROV21xbWY2Wmt6SXZwQ3E3bU9UWmRXcnBldndGbi9KNkZDazNJWlRsdmE2QjlLa1llLzg3UkYwdDN3VDdCVm9XZlI3MGtMRDQvZ29ROUJwMGJiZnNKNlZzTTRKSk5JMFZRd25NeDI1NEVnVWQwMTAzNVVIc1FteG1IUzJkdXA1N1o3MmxOQ2NQWHAzL0hWUExMYVBJa1JwVUkwNVBnVld2a2k5V2lra280dUhZcitqVlQyRjd3RkpsbmtlU0F1NHJBV1NYK25ZWWZDT0pZY3NhV1V4NUF5VFArU3lidW43d2NKYnBjOEV1bHNBY0xQL0N3NnN4eDFJMzZLdEY4bFlIcUhZaWtSeTJOOVFmVTMyZW0wTTZXRC9sa0ZwTGk3b3c4MTNhV2pWaEVVcFYwcTE2UFlwMHRIbjZJNGIiLCJtYWMiOiIxNDBhOWY5MGJiZTIxOGJmOTZhNGQ0NTdjMDNkMTAxOWIwYmQxZTVmMWM3NDc3NDA5MWNhYzMzZDBiMWYxZWRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lYCgmFS8fqkPjKv2BfRzZo9jiALUMXID3HgtHMZN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-734103757 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:33:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5SYys4ZUczMWpMOHBaelRIeWE1RHc9PSIsInZhbHVlIjoicGRPTjlQNXIxYjVZbE9xd1ExRjg2QVZObU1DYjQzRUdZc0Q1dGdJRjNDdG9LdTlrUHlGeHhYbnphdTlxdzU5amtJUVNRQms4YzZ6cmQxZU1ObjNNbjdPeWdYNHZ5eTBZOTA5WEtpenA2aTlESmw3a3Q4ZW5kOXpiLzRZakFEejdKSDRocHhtSmRVRWMyMmpkdGdRUjk5VlYwODI5MnlUVnBOaUVYd3FDa0tpSFkrNGxnWVJUMTlkRERibjliOUl2OW1zejEwUERRSnlNdStXMGZSdjEzdWdjd2puVTVGQmRoSWpBbHo5b3M1bDhCZGhTYmI2TFJoenl1bWVweEU4bVlmQk9iY25ESUlEVGZxT1BXc05hK3hncmlmQzRFanBPUEgyMER6bnBsYm03bWF0VEZhWXZ6M3NhRzBtOFJrUkUwYWVlWUVPVktrYkVBSHdmUE5DQUxIZzE1am1qL29VaVJxNXQ2dEZ3SVJhaEg5RjJvT01saFBINC9MaVh3K0RHWTV4bVkzZFJwS0RjRGtUYWRLeTFYZHczdmNRNHVKblJ2T0pKVlVnTnVKanIwL2VqdW45aWJ5RzdOSTlQNWI2eEpiVDdwNEZQN0hCbmtoYjVQQlZlRzRYdEJwbDJmKzN6b2Ivd1VSR3YxK1I4dnNrY1JaYWhaRWNoeEZ1amkxU2ciLCJtYWMiOiI5YWRkZDE3MjgwMGUzODJkZTkwYzBjZTA2MTk0NmI5ODk0Y2M4MmU4MGY2ZDRhMGM1NjA5ZGVlNTdhN2Q0NzBlIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlJnMGE4Yk1vVExOV3hsNmU2MTBYVUE9PSIsInZhbHVlIjoiTnN1c1FWbUdiRDFBODhHNU5JS0U3QWJmeTBuanFjL1ZKVzJPdk9uTk9JbkhBdTRiUTVRTmtNTW4rYUVILzZEb2dZLzBIOFFLMUxoN2VKMGJEWVFrYVVsY3NJSGNiWkgzVVpyVkI3VGJoZ3EyVGJkTnc2NlJJZzMvM1RWSHlvMHZXYkdMaTNhcytRU2hMWlRrRUN2NU1Kbld6UCt0ZHNsb1VSWlREZFlXdmZmT0JCOFhGQU42LytQU2I3VmdId1IwM3UyeXpaeWVPd2RyVVU1bStMaTJETjNOY3NZWmJsNGdFZ2pwVksrQ1RiL3JzT0dYMGovSDZQVnJyOEF5dGhCVlpBU282YWhyT0g5UzBJakxhd2R4bUZNMG5CMS80MGduNGt1SE5yMjlIb0dqdm42SXVsV2ZhOWtpc1JUOTV5Rm5oVnlwZi9NSFZzL3FiZWpSbURZaVlreHhxOTJUZnJOL3NZRWYxbnBKSkt4SFBjcEpabFlKUVd3Z3VUaGR3NDdaUitjcFNBU09mYy9PTmN2SGxIUVB6b3RhdkZGWXZGNHB5b2xHS3Y1UTRiMjRFOGZ4bkRpVTRPekVXcFNXSXUzRGhzZTFTbkVNZWhaTy9kVWZlUXdvVWhwYzE0UmxhUCtPSnh0ZnFMeG9YNEtzbHJMeFpXWkswRWFZaGlYNFNTNHQiLCJtYWMiOiI1NmY0N2RkNmM1Y2ZlOWI0NWQyNTgwNzQ5MThmZWUyYjIyYTYxYjAxOTYwMWZlN2ZiMzVhOWUxOTM4ZGQ2ZDBlIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5SYys4ZUczMWpMOHBaelRIeWE1RHc9PSIsInZhbHVlIjoicGRPTjlQNXIxYjVZbE9xd1ExRjg2QVZObU1DYjQzRUdZc0Q1dGdJRjNDdG9LdTlrUHlGeHhYbnphdTlxdzU5amtJUVNRQms4YzZ6cmQxZU1ObjNNbjdPeWdYNHZ5eTBZOTA5WEtpenA2aTlESmw3a3Q4ZW5kOXpiLzRZakFEejdKSDRocHhtSmRVRWMyMmpkdGdRUjk5VlYwODI5MnlUVnBOaUVYd3FDa0tpSFkrNGxnWVJUMTlkRERibjliOUl2OW1zejEwUERRSnlNdStXMGZSdjEzdWdjd2puVTVGQmRoSWpBbHo5b3M1bDhCZGhTYmI2TFJoenl1bWVweEU4bVlmQk9iY25ESUlEVGZxT1BXc05hK3hncmlmQzRFanBPUEgyMER6bnBsYm03bWF0VEZhWXZ6M3NhRzBtOFJrUkUwYWVlWUVPVktrYkVBSHdmUE5DQUxIZzE1am1qL29VaVJxNXQ2dEZ3SVJhaEg5RjJvT01saFBINC9MaVh3K0RHWTV4bVkzZFJwS0RjRGtUYWRLeTFYZHczdmNRNHVKblJ2T0pKVlVnTnVKanIwL2VqdW45aWJ5RzdOSTlQNWI2eEpiVDdwNEZQN0hCbmtoYjVQQlZlRzRYdEJwbDJmKzN6b2Ivd1VSR3YxK1I4dnNrY1JaYWhaRWNoeEZ1amkxU2ciLCJtYWMiOiI5YWRkZDE3MjgwMGUzODJkZTkwYzBjZTA2MTk0NmI5ODk0Y2M4MmU4MGY2ZDRhMGM1NjA5ZGVlNTdhN2Q0NzBlIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlJnMGE4Yk1vVExOV3hsNmU2MTBYVUE9PSIsInZhbHVlIjoiTnN1c1FWbUdiRDFBODhHNU5JS0U3QWJmeTBuanFjL1ZKVzJPdk9uTk9JbkhBdTRiUTVRTmtNTW4rYUVILzZEb2dZLzBIOFFLMUxoN2VKMGJEWVFrYVVsY3NJSGNiWkgzVVpyVkI3VGJoZ3EyVGJkTnc2NlJJZzMvM1RWSHlvMHZXYkdMaTNhcytRU2hMWlRrRUN2NU1Kbld6UCt0ZHNsb1VSWlREZFlXdmZmT0JCOFhGQU42LytQU2I3VmdId1IwM3UyeXpaeWVPd2RyVVU1bStMaTJETjNOY3NZWmJsNGdFZ2pwVksrQ1RiL3JzT0dYMGovSDZQVnJyOEF5dGhCVlpBU282YWhyT0g5UzBJakxhd2R4bUZNMG5CMS80MGduNGt1SE5yMjlIb0dqdm42SXVsV2ZhOWtpc1JUOTV5Rm5oVnlwZi9NSFZzL3FiZWpSbURZaVlreHhxOTJUZnJOL3NZRWYxbnBKSkt4SFBjcEpabFlKUVd3Z3VUaGR3NDdaUitjcFNBU09mYy9PTmN2SGxIUVB6b3RhdkZGWXZGNHB5b2xHS3Y1UTRiMjRFOGZ4bkRpVTRPekVXcFNXSXUzRGhzZTFTbkVNZWhaTy9kVWZlUXdvVWhwYzE0UmxhUCtPSnh0ZnFMeG9YNEtzbHJMeFpXWkswRWFZaGlYNFNTNHQiLCJtYWMiOiI1NmY0N2RkNmM1Y2ZlOWI0NWQyNTgwNzQ5MThmZWUyYjIyYTYxYjAxOTYwMWZlN2ZiMzVhOWUxOTM4ZGQ2ZDBlIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734103757\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1069188521 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1069188521\", {\"maxDepth\":0})</script>\n"}}