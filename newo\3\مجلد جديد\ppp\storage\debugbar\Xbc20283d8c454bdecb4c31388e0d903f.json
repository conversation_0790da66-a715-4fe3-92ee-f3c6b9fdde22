{"__meta": {"id": "Xbc20283d8c454bdecb4c31388e0d903f", "datetime": "2025-06-19 12:34:43", "utime": **********.084489, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750336482.016615, "end": **********.084521, "duration": 1.067906141281128, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": 1750336482.016615, "relative_start": 0, "end": 1750336482.968341, "relative_end": 1750336482.968341, "duration": 0.9517261981964111, "duration_str": "952ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750336482.968359, "relative_start": 0.9517440795898438, "end": **********.084525, "relative_end": 4.0531158447265625e-06, "duration": 0.1161661148071289, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46108168, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017650000000000002, "accumulated_duration_str": "17.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.020015, "duration": 0.01575, "duration_str": "15.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.235}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.05252, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.235, "width_percent": 4.589}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.066685, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.824, "width_percent": 6.176}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/support\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2136106500 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2136106500\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-843658491 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-843658491\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-799783195 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799783195\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336441954%7C4%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlGNXBNZzkxOWJZR2RBTE03T1RZRmc9PSIsInZhbHVlIjoiTXR1QVJwempJOU81amhyVk9yM0Y0V1dCK0laS2hLck1DbUx5dzV0TUM1a1Znd2dUWFRiVU1VNTAxUXcvS3ZYbE9WaUhqbG1WbjRtRDVzdU1rZUxVQlhMc0lIUU9uMThMQ1YvWlRoN3RwRFlISmxBUzhubWpoSlN0VWZvL2I1SHVYL0Exa3NtWFhvaEFiQWh2ZmFaSW1kZk5xbmkzMVVOWUFyOXVQWm0vaVY5cHZvS01Eb2dNL1FlOGlUZUVIK3RPNkFuODl4cWJRUVNNNTV6a1lwZDZyWHF0OVNZUXRHTzdKYzNNa2NKc3kwV3MzVzIxWEZEaXI5OVRyVUh5UkwrdXA2c1BlL1FJTjlsNTdIbkJrV0xGSjZQRkxOb3BKZUlwaEgxcEJ3N1k4cjFkKzIvUUg5aUwzVW5FNmUyV1VQSEtJN1ZUdUh5TUZXY3hrR3ZuQjVSNkM0M0pHQnc4bVNJMHFZOTBxRmdsMS9IS21DOFdSRmJpVkhtM2FNK1JGbnIzS3RPK0UrRkZGT3d4OE1pUkFVc0tEYVdjT3R2c1NpMElhSS9wVFNHZHVhNS9ISkFMWWplZVdpa2U5SHpkNkhQQW8ycjlQa1ZNbHprK3ptSUJlbHp6Zk5Od0RKZ3FZNTY2SkhDSFAwdGpKNGZYV05tSWU2dU94WGNUWXFHSzh4dHAiLCJtYWMiOiI4ZGU2Njk3ZDE5MWI2OGQ3NzgxNmMwNjE0ZmFjMmNmNDEzYjdmZjg2M2VhMjBiMDMxY2YwMmRkNmIyMmU3ZjJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IncxU2xmdmVYem9iaHptOEx2Mkd5Nmc9PSIsInZhbHVlIjoiLzVHeVRZWW1WbmxtM3pacm5NVmIxdkxhTlY5ZmU0SDJqMjdZTCtNcjZSQ2VFa1cxZmlzVzhxVGY3VWNOZmo1U0VVV2xySXk3L2owOGl4dkdvSW1YVDErdDlIM3FSRmQ1eTVJTW56YmFwVjdTMldjMThQcDh5bzAxMVoxSC82VlRJbEVNZ3lYTmlxZnVTdlJnc3ErSXFyTDYyS2xTK3FweEJVcHJMeXY1aDlyZW5LcFQ5RCt4ZWdJOWJUV0xQQVE1czc4SzlwVVJhNHNrMWhtd1lEZ2tsaHlGZWpjTmZyM1cxUENhSHZORXVNNElqNE1DVGRTaE5MNVJERG11VWkzVUZRRUZhbnpKb0lZOFVvOGR0TFZSbmFLZ1lCeHphSG1DVTVRcEhCRHZDS2ljMElBNVB2U05KR3M5STdncU1FZm56SmEzYjg5ZVF0RDNyVVdoWkllWm5lNDlwSmI4OHhuTFA4cGdJQXFOR3B1c3ZUR2p4SkhVUndlSUtVS3kwdEY3eUhKSUpubU5JSk1nQTc2bWlkWUtNdkxCcUtiOUxlR1RYSi9EcXBuZDh4cDBVN2RmSVViT3ZOV3BYeTdPR0hsTmFrenIyMHRGa2ZEOXdXVExmUlY2dDl5bjhDdUVxbEQrMUpyZ3NTTXVMS2J1bi9TN3JGZVFZUFhCdThweXhwZlEiLCJtYWMiOiJiNTI5OWQ3ODgyMWYxNmFiMTBlMTA0ZTY5MzhlZWI5OTI5YzkxMzU0OGY3ZDk2ZTlhZjgxNDVkNDcyMmNmMGMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BvzDAp8Ceepvt22TODQJA2h22xCzbnkTzOjAbsGW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-975824415 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:34:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBWdUNPWUFjTWV4bHFaT1dFMFUvS1E9PSIsInZhbHVlIjoiMU1sUzJzT1kvbEZ1cis5UU9FREhsSVBJTmY5SE1lNU1TOHZ3R2FUa1ErOUo3WmgrekdLckNMSEhjT3hETndrczNla0tuRy9ZWTB0VklrZEdIZ1ladzl3ZkVZM3lnNnVqZUN0Qk8xOXZNR3VZeDJYa2RSM0doOUtkNXdjSzl3dkF4M3FxTHByNHFveVRpdk4yU1FjcnVkUEU0VWlaTU9uM2I3UjEzWnZZdnNteVhaNk1CSklWd2paSHMxc1kvVk1CTDBpVWdNOERCdHJaMGZjNVdIMnRsalovUUlRWHJrVjlXclNtU08yU3FZTlZRVkJxdDd4bSt2dVNtUFExQ1UxV2FEcEZzcnFnZHhUSkZkNVo1TzV0V3A2T2g4eWdxY0JOTlJKMFlPM0Fxa3lBQ3VCNG9oQ2FVY1VZYVNlQnByRk1JMWlXVVEySzU5TnMvV3dFOXRJRzNrZ1YwS2M5cCs4YStXZjdLSHdMcVdWZE9EV1hKV3YrR05ab2hZZFNEVmRpSWY2WUdLV3VWMmhvbjcrdFp3eXI1YTc0NUx1azU4YXllVDRIQVkwTHhSWEVXYnI2WFlWN1BXYkl2amFWUnFmYUlMYkd0WEZobUFMZ3pvV1VmUVlSaVVINHBLV3BFTmlIQ3JBekVUMWk4dFRNYVR6ZlI3MW5Ta3kvczRjQUt0cXYiLCJtYWMiOiJmYTQ2MDdiMzAyMTM3ZGEwODg4NmY4ZGM5NGVkN2M2NWMxZmVlYWEzNTQ0YjRmNmM2MjRhM2E5MGYyNjVlNDI5IiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:34:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9vVW5zKzVZdWpMK3VuRGkyOU9LK1E9PSIsInZhbHVlIjoiL2VRT09PY2FqSytFdjFLZGpmQ3BQZDZGTzFrZ055RTBZTUlEWUxwUFpMTnZuVzdhYytQL1VUN3JwSWlTRWU4T0FCQUJRTkhPSmtibzN0Y2d1WlR3dDBLQ3BYQTZpVlgvT1J1TEl1ZS9hZ2dwUk1nRkRQZDZobUwwL1BZa25kb2l4VmxJL2UyL0JNdlhTU1ZKNU1IcTlsWC9hb0FCai9OclIrQ3RMMHpFNkUwa2wyNVpVckdwZ1l1cEFGUGNtenA0eDdoZU9lcmtwOVBJTU5qRFYvb3JDVmZENkRYdU1KQlV0T0ZBdmZnTkVjdEg4OGlRSVF1ak5uNWcrcVF5NDgrT09FdkFyUmdJWUZYQ2w4enhaRmprbzVHL2FGZ3JVRGdGMGV5aVZnL1VwZnQ1S05KR1pvVU13dGRCdXgzQWp6ejVYWXJUYkhOSkxHN1FQazRBMEZ4Tk8xeW1DUUlVL3RFZ2VrT0M2WFB3cDV3NU0wdlZRc1B1NkxrTHpRcVVoOTI3ZW8xaEY0K3VRcGZzNThyOW4vdWVheTFhdkVMTTBZRFpDOUtaT3N6aHZLeHp1RGM3SnFoajZHMW1FM0VvVGxHTWxEWC8yRXloc0VieS9SZHJSajVhbldpalB6Y3Q0SGhja0ZRSHdDa0JrZDRXYzJEYmYyTVVYZVBOcXhtWjNTamwiLCJtYWMiOiIwMzc0MjY4YzA1ZGRlNzFhNTI2MTIwYTJjN2QzOTQxMWQ1ZDU0YzJhZWExZmU3MjA5YmQ4MTA0ODMxZjVmN2VkIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:34:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBWdUNPWUFjTWV4bHFaT1dFMFUvS1E9PSIsInZhbHVlIjoiMU1sUzJzT1kvbEZ1cis5UU9FREhsSVBJTmY5SE1lNU1TOHZ3R2FUa1ErOUo3WmgrekdLckNMSEhjT3hETndrczNla0tuRy9ZWTB0VklrZEdIZ1ladzl3ZkVZM3lnNnVqZUN0Qk8xOXZNR3VZeDJYa2RSM0doOUtkNXdjSzl3dkF4M3FxTHByNHFveVRpdk4yU1FjcnVkUEU0VWlaTU9uM2I3UjEzWnZZdnNteVhaNk1CSklWd2paSHMxc1kvVk1CTDBpVWdNOERCdHJaMGZjNVdIMnRsalovUUlRWHJrVjlXclNtU08yU3FZTlZRVkJxdDd4bSt2dVNtUFExQ1UxV2FEcEZzcnFnZHhUSkZkNVo1TzV0V3A2T2g4eWdxY0JOTlJKMFlPM0Fxa3lBQ3VCNG9oQ2FVY1VZYVNlQnByRk1JMWlXVVEySzU5TnMvV3dFOXRJRzNrZ1YwS2M5cCs4YStXZjdLSHdMcVdWZE9EV1hKV3YrR05ab2hZZFNEVmRpSWY2WUdLV3VWMmhvbjcrdFp3eXI1YTc0NUx1azU4YXllVDRIQVkwTHhSWEVXYnI2WFlWN1BXYkl2amFWUnFmYUlMYkd0WEZobUFMZ3pvV1VmUVlSaVVINHBLV3BFTmlIQ3JBekVUMWk4dFRNYVR6ZlI3MW5Ta3kvczRjQUt0cXYiLCJtYWMiOiJmYTQ2MDdiMzAyMTM3ZGEwODg4NmY4ZGM5NGVkN2M2NWMxZmVlYWEzNTQ0YjRmNmM2MjRhM2E5MGYyNjVlNDI5IiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:34:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9vVW5zKzVZdWpMK3VuRGkyOU9LK1E9PSIsInZhbHVlIjoiL2VRT09PY2FqSytFdjFLZGpmQ3BQZDZGTzFrZ055RTBZTUlEWUxwUFpMTnZuVzdhYytQL1VUN3JwSWlTRWU4T0FCQUJRTkhPSmtibzN0Y2d1WlR3dDBLQ3BYQTZpVlgvT1J1TEl1ZS9hZ2dwUk1nRkRQZDZobUwwL1BZa25kb2l4VmxJL2UyL0JNdlhTU1ZKNU1IcTlsWC9hb0FCai9OclIrQ3RMMHpFNkUwa2wyNVpVckdwZ1l1cEFGUGNtenA0eDdoZU9lcmtwOVBJTU5qRFYvb3JDVmZENkRYdU1KQlV0T0ZBdmZnTkVjdEg4OGlRSVF1ak5uNWcrcVF5NDgrT09FdkFyUmdJWUZYQ2w4enhaRmprbzVHL2FGZ3JVRGdGMGV5aVZnL1VwZnQ1S05KR1pvVU13dGRCdXgzQWp6ejVYWXJUYkhOSkxHN1FQazRBMEZ4Tk8xeW1DUUlVL3RFZ2VrT0M2WFB3cDV3NU0wdlZRc1B1NkxrTHpRcVVoOTI3ZW8xaEY0K3VRcGZzNThyOW4vdWVheTFhdkVMTTBZRFpDOUtaT3N6aHZLeHp1RGM3SnFoajZHMW1FM0VvVGxHTWxEWC8yRXloc0VieS9SZHJSajVhbldpalB6Y3Q0SGhja0ZRSHdDa0JrZDRXYzJEYmYyTVVYZVBOcXhtWjNTamwiLCJtYWMiOiIwMzc0MjY4YzA1ZGRlNzFhNTI2MTIwYTJjN2QzOTQxMWQ1ZDU0YzJhZWExZmU3MjA5YmQ4MTA0ODMxZjVmN2VkIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:34:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-975824415\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-606815387 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-606815387\", {\"maxDepth\":0})</script>\n"}}