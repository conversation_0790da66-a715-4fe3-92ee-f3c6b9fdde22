{"__meta": {"id": "X24521cd2706962d18167b90e0886805b", "datetime": "2025-06-19 12:33:39", "utime": **********.621318, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750336418.546271, "end": **********.621341, "duration": 1.0750699043273926, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1750336418.546271, "relative_start": 0, "end": **********.48838, "relative_end": **********.48838, "duration": 0.9421088695526123, "duration_str": "942ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.488397, "relative_start": 0.9421257972717285, "end": **********.621344, "relative_end": 3.0994415283203125e-06, "duration": 0.13294720649719238, "duration_str": "133ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46148192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01393, "accumulated_duration_str": "13.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5454261, "duration": 0.00908, "duration_str": "9.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.183}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.571872, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.183, "width_percent": 9.978}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.589762, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 75.162, "width_percent": 12.276}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.605488, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.437, "width_percent": 12.563}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1450023285 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1450023285\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-23819690 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-23819690\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1104955379 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1104955379\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336400672%7C1%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikpjc3lHTUdTaUVhV1N2YjJuQWk1K2c9PSIsInZhbHVlIjoiSW9LWmJocXBLZDdlekxnUXRXR1dJOHNkS0U0ZjFCYnRnMFA2K2ErL0tsTU44U3R1alFEdWFML0krZDhqcVI0eFJjZ25pUFUza1ZibUFtRnJXWlFMKzF6ZnVWOWRhcHoxQnUvMXVDU2NXNERtK0xMK05DSTBPUWNsTjAweGxCamdnQVVRTXhTdE53b091Mm8xbC9hVUJzMVY4RUo4NGxEMHJrUmJuTFBaSE9PcXhFRkpaZTJFZ0NCejJ1TnNTekpYVFBlMFVxZmE2bE5wVlNJZnlCazRFcmthQkUvVUNvRHpJVElleGwxQ1duSWU1aE1vTkZYM255VTlTVWN6ejYraGowYkxtUkMrQ3IwVldJQmgwYjJSOEMrUm02aFBKWm9TTm1zUE5RVE55T0JPdXZ5ekwwYWpUNktVdk4rMHpMZzNCYlhzNWFMMFh3cVVVUUlYaUpSRGVKZVdmRHdZQTk0TitKc09ERXFXclEvN0ozNWNrc1FuR1VITDJxT3RKWm90TVNjdnZqNWgyeG91YnhDUk4vek5YMFdKVmNlODFncVEwNHRvRnVqOVg0VG96NUFFeDVjeHZaWlN4S3JSVGJ4QnVxWVErZ1dSNnhtQUp2dUw1dXlISTV1cjZMaWtKQUN0M2c1VkV1RjNFaDZUNlVBQ0djL1JwSnRqWkdRMkZpNTgiLCJtYWMiOiI1MTgxNDY1OGRlOWM5NmY5MWI3MzM5ZGRhYTE0NzJhOGFhYTU5YmQwYWY1MDAwYzIxZjNhYTg1YjU2OTFjNDFkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNoVHhPdm5wL3ZpR0ErNXlweElJdlE9PSIsInZhbHVlIjoiVDRpM01sYUdkZ0JFcFZDNElRSzRHOC95Ym5nNXpJQzVDMnNiNlFaU3NiQlBjSUV5Y1lkUDNsSE5TY21xSFc3eGZvVGJTTFJTSFQxaVFGNC9PSzliV1NSYkFnTTRleldMWkZ1U2l3V0czWEhCbTN5UWhXVXRQbHc3Yjl6NGoxT1Q4UmdXU0cxYTdYVVNjcjFxYS9ZNjdQUnJucUJjdVhQS3JSaDJ5Vk5vck9MYVViQ0UwcmErek1BQ25yVnQwajV1eHd6Z2ZNTFlydjlFMkE5OTJNNkNWeGRZM1VKTzJndHE5WUcyYkZPZkRPNThxTXZxVlhzYWVHZG5XcXd1ZFd0S1QwK3YzV0psMGhVRHVsdUxSUFQ4MmJWRklWTGpjczU4bys5SXVyS1VHWHhlaWZRWFFPYjdKUlo1MjA4ZDZRTm5tTWFMUWxpVzN4M3FmWjcyUUlJZVZzb0JDSy85WlVpNkFqSTM0SFJ0d3J6bmgwV2IxWkxlL2hmNEZqRElibld2VEVJQ0s0VnQ2WUxOWEhOWk9FdElBdWdWMm8rdTRQUFVuQTU2dW9zMnlla25UZnc0NUVTUGFsYzVkVkFTSkpzOENHUGQ4cGNMRHl6L3FZOGFkdlFiQVpaS291aTN5amh1WnVDeHptTm9ldGgwZGprYTgyaVA1d2J1Z2FZOGI5OWUiLCJtYWMiOiI2NGIzNjhhMzhhMTMyNDBjNDI4YzMwZDVhNjQzNjk0ZTk3NDdjMjMyZmI1MmU0NGMyMTUzN2QxODAyOGMzYzljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-42427033 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lYCgmFS8fqkPjKv2BfRzZo9jiALUMXID3HgtHMZN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-42427033\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1114969816 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:33:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlnWmR2eEV0NnpnMzN6RDFGYnZkR0E9PSIsInZhbHVlIjoiZGZBYmVBV29VemFOU2YrM0VQOFVaUmlWVHk1VW5ZalMwcjErOGdVRUpOQnVJL2wvZTkwQlpYN01IUGhYYnZEaDJDb1k5SkFnbEJDR1crMGVIbXVkYTV4NVZUS3dDRTNnYXMvWXovWkxxcnpnQU9BOTZOa0NyUlR0aFY3Y3NreHorZ2NCNVdRcm0xdFFhaUVZajV0M2VBSitnNk1VeDFJU2lRdE1FNVBKYUEzUlo5RVdhbTMwcjRhU3RBTXFmVVA0aG11c240OFVsQm1BRVBXMW1jeGFsR2R3VTVtcVM5cHlTRmVTVVA1dnJ3LzBBdkVMdDFsUmthNGhpSFJHVHIyT2xRNG5sNGRxcmQ4SWlCcno5anc4elc4a0l5aExFbkJoV3g0QnQvYXcxcTlLTEdnTXlhTHdZV1FxWCtLN0Q0bnFXVFkyWW9LZUVsVjJ2RmRzQlVsM0x5MkVvdm1McExHWVVDcldsdmZta1lDM0pCLzdhdHAxM3hEU3phV0ZBRGlWcVFVODlLbU8zd0VkTGVDaGFJQnk0UUhaV0swai9FQ2t3OGsvL1FMWVBvb3diZDF0T1AyRUhGcStRQmRVVWtsV1M0SHlKMUJraGpOQjREcWFKY29Da0RRZldLSjR1cDE5NmVJQytKbDlwRmpySDB3dUhGZjJiVVdSVUMzVnlmRmciLCJtYWMiOiIzM2ZhMjQyZDYzMmI1NmY1NjcxNTFjMWNmY2M2NDk1NTNjN2E5NGJlOTk4ZDk1MmNlNmNiZjNjODViNjI0ZmJjIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdKYU50S2JJRU5DS1ZvQVRpM3BTbXc9PSIsInZhbHVlIjoiOFJ3R0xLMzdPYzc5UFZ4d28zMHF5cUFrS2I3REZ3M0pUM3RVV3RZUzcxNXVYYUN6cEoxbU4vT21WZU5keEFzTWFwZ1hhTkQrdmxsQ1RtZ2RlUnA4OW9namdCMEhPR2Frc1RzYXN1aXFZOHREdzhHdzhWWDdGbTQ0d05qSW03UUF4V2FqMDlSNFFQVzdpV3o4VldOd1RRU05wdGRLZ3VINDZkVzBrN0MvQ2pKdi91OUZoeFp1bDRrYnJJNVh6ZWhrTS9XYmROV21xbWY2Wmt6SXZwQ3E3bU9UWmRXcnBldndGbi9KNkZDazNJWlRsdmE2QjlLa1llLzg3UkYwdDN3VDdCVm9XZlI3MGtMRDQvZ29ROUJwMGJiZnNKNlZzTTRKSk5JMFZRd25NeDI1NEVnVWQwMTAzNVVIc1FteG1IUzJkdXA1N1o3MmxOQ2NQWHAzL0hWUExMYVBJa1JwVUkwNVBnVld2a2k5V2lra280dUhZcitqVlQyRjd3RkpsbmtlU0F1NHJBV1NYK25ZWWZDT0pZY3NhV1V4NUF5VFArU3lidW43d2NKYnBjOEV1bHNBY0xQL0N3NnN4eDFJMzZLdEY4bFlIcUhZaWtSeTJOOVFmVTMyZW0wTTZXRC9sa0ZwTGk3b3c4MTNhV2pWaEVVcFYwcTE2UFlwMHRIbjZJNGIiLCJtYWMiOiIxNDBhOWY5MGJiZTIxOGJmOTZhNGQ0NTdjMDNkMTAxOWIwYmQxZTVmMWM3NDc3NDA5MWNhYzMzZDBiMWYxZWRlIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:33:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlnWmR2eEV0NnpnMzN6RDFGYnZkR0E9PSIsInZhbHVlIjoiZGZBYmVBV29VemFOU2YrM0VQOFVaUmlWVHk1VW5ZalMwcjErOGdVRUpOQnVJL2wvZTkwQlpYN01IUGhYYnZEaDJDb1k5SkFnbEJDR1crMGVIbXVkYTV4NVZUS3dDRTNnYXMvWXovWkxxcnpnQU9BOTZOa0NyUlR0aFY3Y3NreHorZ2NCNVdRcm0xdFFhaUVZajV0M2VBSitnNk1VeDFJU2lRdE1FNVBKYUEzUlo5RVdhbTMwcjRhU3RBTXFmVVA0aG11c240OFVsQm1BRVBXMW1jeGFsR2R3VTVtcVM5cHlTRmVTVVA1dnJ3LzBBdkVMdDFsUmthNGhpSFJHVHIyT2xRNG5sNGRxcmQ4SWlCcno5anc4elc4a0l5aExFbkJoV3g0QnQvYXcxcTlLTEdnTXlhTHdZV1FxWCtLN0Q0bnFXVFkyWW9LZUVsVjJ2RmRzQlVsM0x5MkVvdm1McExHWVVDcldsdmZta1lDM0pCLzdhdHAxM3hEU3phV0ZBRGlWcVFVODlLbU8zd0VkTGVDaGFJQnk0UUhaV0swai9FQ2t3OGsvL1FMWVBvb3diZDF0T1AyRUhGcStRQmRVVWtsV1M0SHlKMUJraGpOQjREcWFKY29Da0RRZldLSjR1cDE5NmVJQytKbDlwRmpySDB3dUhGZjJiVVdSVUMzVnlmRmciLCJtYWMiOiIzM2ZhMjQyZDYzMmI1NmY1NjcxNTFjMWNmY2M2NDk1NTNjN2E5NGJlOTk4ZDk1MmNlNmNiZjNjODViNjI0ZmJjIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdKYU50S2JJRU5DS1ZvQVRpM3BTbXc9PSIsInZhbHVlIjoiOFJ3R0xLMzdPYzc5UFZ4d28zMHF5cUFrS2I3REZ3M0pUM3RVV3RZUzcxNXVYYUN6cEoxbU4vT21WZU5keEFzTWFwZ1hhTkQrdmxsQ1RtZ2RlUnA4OW9namdCMEhPR2Frc1RzYXN1aXFZOHREdzhHdzhWWDdGbTQ0d05qSW03UUF4V2FqMDlSNFFQVzdpV3o4VldOd1RRU05wdGRLZ3VINDZkVzBrN0MvQ2pKdi91OUZoeFp1bDRrYnJJNVh6ZWhrTS9XYmROV21xbWY2Wmt6SXZwQ3E3bU9UWmRXcnBldndGbi9KNkZDazNJWlRsdmE2QjlLa1llLzg3UkYwdDN3VDdCVm9XZlI3MGtMRDQvZ29ROUJwMGJiZnNKNlZzTTRKSk5JMFZRd25NeDI1NEVnVWQwMTAzNVVIc1FteG1IUzJkdXA1N1o3MmxOQ2NQWHAzL0hWUExMYVBJa1JwVUkwNVBnVld2a2k5V2lra280dUhZcitqVlQyRjd3RkpsbmtlU0F1NHJBV1NYK25ZWWZDT0pZY3NhV1V4NUF5VFArU3lidW43d2NKYnBjOEV1bHNBY0xQL0N3NnN4eDFJMzZLdEY4bFlIcUhZaWtSeTJOOVFmVTMyZW0wTTZXRC9sa0ZwTGk3b3c4MTNhV2pWaEVVcFYwcTE2UFlwMHRIbjZJNGIiLCJtYWMiOiIxNDBhOWY5MGJiZTIxOGJmOTZhNGQ0NTdjMDNkMTAxOWIwYmQxZTVmMWM3NDc3NDA5MWNhYzMzZDBiMWYxZWRlIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:33:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114969816\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1427174184 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1427174184\", {\"maxDepth\":0})</script>\n"}}