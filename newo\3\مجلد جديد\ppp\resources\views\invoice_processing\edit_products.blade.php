@extends('layouts.admin')
@section('page-title')
    {{__('تعديل منتجات الفاتورة')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item"><a href="{{route('invoice.processing.invoice.processor')}}">{{__('معالج فواتير البيع')}}</a></li>
    <li class="breadcrumb-item">{{__('تعديل منتجات الفاتورة')}} #{{ Auth::user()->posNumberFormat($pos->id) }}</li>
@endsection

@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- Font Awesome for icons (if not already included) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        .product-row {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .remove-product {
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .add-product {
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            cursor: pointer;
        }
        .invoice-summary {
            background-color: #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            position: sticky;
            top: 20px;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .product-select {
            max-width: 100%;
        }

        /* تخصيص Select2 ليتناسق مع Bootstrap */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--bootstrap-5 .select2-selection {
            min-height: calc(1.5em + 0.75rem + 2px);
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            background-color: #fff;
            background-image: none;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .select2-container--bootstrap-5 .select2-selection--single {
            height: calc(1.5em + 0.75rem + 2px);
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            padding: 0;
            line-height: calc(1.5em + 0.75rem);
            color: #212529;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            height: calc(1.5em + 0.75rem);
            right: 0.75rem;
            width: 20px;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow b {
            border-color: #6c757d transparent transparent transparent;
            border-style: solid;
            border-width: 5px 4px 0 4px;
            height: 0;
            left: 50%;
            margin-left: -4px;
            margin-top: -2px;
            position: absolute;
            top: 50%;
            width: 0;
        }

        .select2-dropdown {
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .select2-search--dropdown {
            padding: 0.5rem;
        }

        .select2-search--dropdown .select2-search__field {
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            width: 100%;
        }

        .select2-results__option {
            padding: 0.5rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
        }

        .select2-results__option--highlighted {
            background-color: var(--bs-primary);
            color: #fff;
        }

        .select2-results__option[aria-selected="true"] {
            background-color: var(--bs-light);
            color: var(--bs-dark);
        }

        .select2-container--bootstrap-5.select2-container--focus .select2-selection,
        .select2-container--bootstrap-5.select2-container--open .select2-selection {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        .select2-container--bootstrap-5 .select2-selection[aria-expanded="true"] .select2-selection__arrow b {
            border-color: transparent transparent #6c757d transparent;
            border-width: 0 4px 5px 4px;
        }

        /* تحسين عرض النتائج */
        .product-option {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .product-option-title {
            font-weight: 600;
            color: #212529;
        }

        .product-option-details {
            font-size: 0.875rem;
            color: #6c757d;
            display: flex;
            gap: 1rem;
        }

        .product-option-price,
        .product-option-tax {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        /* تحسينات إضافية للتجاوب */
        @media (max-width: 768px) {
            .select2-dropdown {
                width: 100% !important;
            }

            .product-option-details {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* تحسين مظهر placeholder */
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__placeholder {
            color: #6c757d;
            font-style: italic;
        }

        /* تحسين مظهر clear button */
        .select2-container--bootstrap-5 .select2-selection__clear {
            color: #6c757d;
            cursor: pointer;
            float: right;
            font-weight: bold;
            margin-right: 10px;
        }

        .select2-container--bootstrap-5 .select2-selection__clear:hover {
            color: #495057;
        }

        /* تحسين loading state */
        .select2-results__option--loading {
            text-align: center;
            padding: 1rem;
            color: #6c757d;
        }

        /* تحسين disabled state */
        .select2-container--bootstrap-5.select2-container--disabled .select2-selection {
            background-color: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
        }
    </style>
@endpush

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="mb-0">{{ __('تعديل منتجات الفاتورة') }} #{{ Auth::user()->posNumberFormat($pos->id) }}</h5>
                        </div>
                        <div class="col-6 text-end">
                            <a href="{{ route('invoice.processing.invoice.processor') }}" class="btn btn-secondary">
                                <i class="ti ti-arrow-left"></i> {{ __('العودة') }}
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الفاتورة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <strong>{{ __('التاريخ') }}:</strong> {{ Auth::user()->dateFormat($pos->pos_date) }}
                        </div>
                        <div class="col-md-3">
                            <strong>{{ __('العميل') }}:</strong> {{ !empty($pos->customer) ? $pos->customer->name : __('Walk-in Customer') }}
                        </div>
                        <div class="col-md-3">
                            <strong>{{ __('المستودع') }}:</strong> {{ !empty($pos->warehouse) ? $pos->warehouse->name : '' }}
                        </div>
                        <div class="col-md-3">
                            <strong>{{ __('الحالة') }}:</strong> 
                            @if($pos->status_type == 'returned')
                                <span class="badge bg-danger">{{ __('مرتجع بضاعة') }}</span>
                            @elseif($pos->status_type == 'cancelled')
                                <span class="badge bg-secondary">{{ __('ملغية') }}</span>
                            @else
                                <span class="badge bg-success">{{ __('عادي') }}</span>
                            @endif
                        </div>
                    </div>

                    <!-- عرض رسائل الخطأ -->
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>{{ __('تحذيرات المخزون') }}</h6>
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('invoice.processing.update.products', $pos->id) }}" method="POST" id="editProductsForm">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-3">{{ __('منتجات الفاتورة') }} <span id="products-count" class="badge bg-primary">{{ count($pos->items) }}</span></h6>
                                
                                <div id="products-container">
                                    @foreach($pos->items as $index => $item)
                                        <div class="product-row" data-index="{{ $index }}">
                                            <div class="row align-items-center">
                                                <div class="col-md-3">
                                                    <label class="form-label">{{ __('المنتج') }}</label>
                                                    <select name="products[{{ $index }}][product_id]" class="form-control product-select" required>
                                                        <option value="">{{ __('اختر المنتج') }}</option>
                                                        @foreach($products as $product)
                                                            <option value="{{ $product->id }}"
                                                                data-price="{{ $product->sale_price }}"
                                                                data-tax="{{ $product->taxes ? $product->taxes->rate : '15.00' }}"
                                                                data-stock="{{ $product->available_quantity }}"
                                                                {{ $item->product_id == $product->id ? 'selected' : '' }}>
                                                                {{ $product->name }} ({{ $product->sku }})
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label">{{ __('الكمية') }}</label>
                                                    <input type="number" name="products[{{ $index }}][quantity]" 
                                                           class="form-control quantity-input" 
                                                           value="{{ $item->quantity }}" 
                                                           min="1" step="1" required>
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label">{{ __('السعر') }}</label>
                                                    <input type="number" name="products[{{ $index }}][price]" 
                                                           class="form-control price-input" 
                                                           value="{{ $item->price }}" 
                                                           min="0" step="0.01" required>
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label">{{ __('الخصم') }}</label>
                                                    <input type="number" name="products[{{ $index }}][discount]" 
                                                           class="form-control discount-input" 
                                                           value="{{ $item->discount }}" 
                                                           min="0" step="0.01">
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label">{{ __('الضريبة') }}</label>
                                                    <input type="text" name="products[{{ $index }}][tax]" 
                                                           class="form-control tax-input" 
                                                           value="{{ $item->tax }}" 
                                                           placeholder="15.00">
                                                </div>
                                                <div class="col-md-1">
                                                    <label class="form-label">&nbsp;</label>
                                                    <button type="button" class="remove-product d-block" onclick="removeProduct(this)">
                                                        <i class="ti ti-x"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-md-11">
                                                    <label class="form-label">{{ __('الوصف') }}</label>
                                                    <input type="text" name="products[{{ $index }}][description]" 
                                                           class="form-control" 
                                                           value="{{ $item->description }}" 
                                                           placeholder="{{ __('وصف اختياري للمنتج') }}">
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <button type="button" class="add-product" onclick="addProduct()">
                                    <i class="ti ti-plus"></i> {{ __('إضافة منتج') }}
                                </button>
                            </div>

                            <div class="col-md-4">
                                <!-- ملخص الفاتورة -->
                                <div class="invoice-summary">
                                    <h6 class="mb-3">{{ __('ملخص الفاتورة') }}</h6>
                                    <div class="row mb-2">
                                        <div class="col-6">{{ __('المجموع الفرعي') }}:</div>
                                        <div class="col-6 text-end" id="subtotal">{{ Auth::user()->priceFormat($pos->getSubTotal()) }}</div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-6">{{ __('الخصم') }}:</div>
                                        <div class="col-6 text-end" id="total-discount">{{ Auth::user()->priceFormat($pos->getTotalDiscount()) }}</div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-6">{{ __('الضريبة') }}:</div>
                                        <div class="col-6 text-end" id="total-tax">{{ Auth::user()->priceFormat($pos->getTotalTax()) }}</div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-6"><strong>{{ __('الإجمالي') }}:</strong></div>
                                        <div class="col-6 text-end"><strong id="total">{{ Auth::user()->priceFormat($pos->getTotal()) }}</strong></div>
                                    </div>
                                </div>

                                <!-- معلومات الدفع -->
                                <div class="payment-info mt-4">
                                    <h6 class="mb-3">{{ __('معلومات الدفع') }}</h6>

                                    <!-- نوع الدفع -->
                                    <div class="form-group mb-3">
                                        <label class="form-label">{{ __('طريقة الدفع') }}</label>
                                        <select name="payment_type" id="payment_type" class="form-control" required>
                                            <option value="cash" {{ (isset($pos->posPayment) && $pos->posPayment->payment_type == 'cash') ? 'selected' : '' }}>
                                                {{ __('نقد') }} 💵
                                            </option>
                                            <option value="network" {{ (isset($pos->posPayment) && $pos->posPayment->payment_type == 'network') ? 'selected' : '' }}>
                                                {{ __('شبكة') }} 💳
                                            </option>
                                            <option value="split" {{ (isset($pos->posPayment) && $pos->posPayment->payment_type == 'split') ? 'selected' : '' }}>
                                                {{ __('مقسم') }} 💰
                                            </option>
                                        </select>
                                    </div>

                                    <!-- المبلغ النقدي -->
                                    <div class="form-group mb-3" id="cash_amount_group">
                                        <label class="form-label">{{ __('المبلغ النقدي') }}</label>
                                        <input type="number" name="cash_amount" id="cash_amount"
                                               class="form-control"
                                               value="{{ isset($pos->posPayment) ? $pos->posPayment->cash_amount : $pos->getTotal() }}"
                                               min="0" step="0.01">
                                    </div>

                                    <!-- مبلغ الشبكة -->
                                    <div class="form-group mb-3" id="network_amount_group" style="display: none;">
                                        <label class="form-label">{{ __('مبلغ الشبكة') }}</label>
                                        <input type="number" name="network_amount" id="network_amount"
                                               class="form-control"
                                               value="{{ isset($pos->posPayment) ? $pos->posPayment->network_amount : 0 }}"
                                               min="0" step="0.01">
                                    </div>

                                    <!-- رقم المعاملة -->
                                    <div class="form-group mb-3" id="transaction_number_group" style="display: none;">
                                        <label class="form-label">{{ __('رقم المعاملة') }}</label>
                                        <input type="text" name="transaction_number" id="transaction_number"
                                               class="form-control"
                                               value="{{ isset($pos->posPayment) ? $pos->posPayment->transaction_number : '' }}"
                                               placeholder="{{ __('رقم المعاملة (اختياري)') }}">
                                    </div>

                                    <!-- تاريخ الدفع -->
                                    <div class="form-group mb-3">
                                        <label class="form-label">{{ __('تاريخ الدفع') }}</label>
                                        <input type="date" name="payment_date" id="payment_date"
                                               class="form-control"
                                               value="{{ isset($pos->posPayment) ? $pos->posPayment->date : $pos->pos_date }}"
                                               required>
                                    </div>

                                    <!-- خصم الدفع -->
                                    <div class="form-group mb-3">
                                        <label class="form-label">{{ __('خصم الدفع') }}</label>
                                        <input type="number" name="payment_discount" id="payment_discount"
                                               class="form-control"
                                               value="{{ isset($pos->posPayment) ? $pos->posPayment->discount : 0 }}"
                                               min="0" step="0.01">
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="ti ti-device-floppy"></i> {{ __('حفظ التعديلات') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<!-- jQuery (إذا لم يكن موجوداً) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<style>
    .payment-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
    }

    .payment-info h6 {
        color: #495057;
        border-bottom: 2px solid #007bff;
        padding-bottom: 8px;
        margin-bottom: 20px;
    }

    .form-group label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    #payment_type {
        background: linear-gradient(45deg, #f8f9fa, #ffffff);
        border: 2px solid #dee2e6;
        font-weight: 600;
    }

    #payment_type:focus {
        border-color: #007bff;
        background: #ffffff;
    }

    .invoice-summary {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 20px;
    }

    .invoice-summary h6 {
        color: #1976d2;
        border-bottom: 2px solid #1976d2;
        padding-bottom: 8px;
        margin-bottom: 20px;
    }
</style>
<script>
    let productIndex = {{ count($pos->items) }};

    function addProduct() {
        const container = document.getElementById('products-container');
        const productRow = document.createElement('div');
        productRow.className = 'product-row';
        productRow.setAttribute('data-index', productIndex);
        
        productRow.innerHTML = `
            <div class="row align-items-center">
                <div class="col-md-3">
                    <label class="form-label">{{ __('المنتج') }}</label>
                    <select name="products[${productIndex}][product_id]" class="form-control product-select" required>
                        <option value="">{{ __('اختر المنتج') }}</option>
                        @foreach($products as $product)
                            <option value="{{ $product->id }}"
                                data-price="{{ $product->sale_price }}"
                                data-tax="{{ $product->taxes ? $product->taxes->rate : '15.00' }}"
                                data-stock="{{ $product->available_quantity }}">
                                {{ $product->name }} ({{ $product->sku }})
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('الكمية') }}</label>
                    <input type="number" name="products[${productIndex}][quantity]" 
                           class="form-control quantity-input" 
                           value="1" min="1" step="1" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('السعر') }}</label>
                    <input type="number" name="products[${productIndex}][price]" 
                           class="form-control price-input" 
                           value="0" min="0" step="0.01" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('الخصم') }}</label>
                    <input type="number" name="products[${productIndex}][discount]" 
                           class="form-control discount-input" 
                           value="0" min="0" step="0.01">
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('الضريبة') }}</label>
                    <input type="text" name="products[${productIndex}][tax]" 
                           class="form-control tax-input" 
                           value="15.00" placeholder="15.00">
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button type="button" class="remove-product d-block" onclick="removeProduct(this)">
                        <i class="ti ti-x"></i>
                    </button>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-11">
                    <label class="form-label">{{ __('الوصف') }}</label>
                    <input type="text" name="products[${productIndex}][description]" 
                           class="form-control" 
                           placeholder="{{ __('وصف اختياري للمنتج') }}">
                </div>
            </div>
        `;
        
        container.appendChild(productRow);
        productIndex++;
        
        // إضافة event listeners للمنتج الجديد
        attachProductEvents(productRow);

        // إضافة event listener لزر الحذف
        const removeButton = productRow.querySelector('.remove-product');
        if (removeButton) {
            removeButton.addEventListener('click', function() {
                removeProduct(this);
            });
        }

        calculateTotals();
        updateProductsCount();
    }

    function updateProductsCount() {
        const productRows = document.querySelectorAll('.product-row');
        const countElement = document.getElementById('products-count');
        if (countElement) {
            countElement.textContent = productRows.length;
        }

        // تعطيل أزرار الحذف إذا كان هناك منتج واحد فقط
        const removeButtons = document.querySelectorAll('.remove-product');
        removeButtons.forEach(button => {
            if (productRows.length <= 1) {
                button.style.opacity = '0.5';
                button.style.cursor = 'not-allowed';
                button.disabled = true;
                button.title = '{{ __("لا يمكن حذف آخر منتج في الفاتورة") }}';
            } else {
                button.style.opacity = '1';
                button.style.cursor = 'pointer';
                button.disabled = false;
                button.title = '{{ __("حذف المنتج") }}';
            }
        });
    }

    function removeProduct(button) {
        console.log('removeProduct called', button);

        // التحقق من أن الزر غير معطل
        if (button.disabled) {
            console.log('Button is disabled, returning');
            return;
        }

        const productRow = button.closest('.product-row');
        const allProductRows = document.querySelectorAll('.product-row');

        console.log('Product rows count:', allProductRows.length);

        // التحقق من وجود منتج واحد على الأقل
        if (allProductRows.length <= 1) {
            alert('{{ __("لا يمكن حذف جميع المنتجات. يجب أن تحتوي الفاتورة على منتج واحد على الأقل.") }}');
            return;
        }

        // طلب تأكيد الحذف
        if (!confirm('{{ __("هل أنت متأكد من حذف هذا المنتج؟") }}')) {
            return;
        }

        // إزالة Select2 قبل حذف العنصر
        const productSelect = productRow.querySelector('.product-select');
        if (productSelect && $(productSelect).hasClass('select2-hidden-accessible')) {
            $(productSelect).select2('destroy');
        }

        productRow.remove();
        calculateTotals();
        updateProductsCount();

        // إظهار رسالة نجاح
        console.log('تم حذف المنتج بنجاح');
    }

    function initializeSelect2(selectElement) {
        $(selectElement).select2({
            theme: 'bootstrap-5',
            placeholder: '{{ __("ابحث عن المنتج...") }}',
            allowClear: true,
            width: '100%',
            dropdownAutoWidth: true,
            language: {
                noResults: function() {
                    return '<div class="text-center p-2">{{ __("لا توجد نتائج") }}</div>';
                },
                searching: function() {
                    return '<div class="text-center p-2"><i class="fas fa-spinner fa-spin me-2"></i>{{ __("جاري البحث...") }}</div>';
                },
                inputTooShort: function() {
                    return '<div class="text-center p-2 text-muted">{{ __("اكتب للبحث") }}</div>';
                },
                loadingMore: function() {
                    return '<div class="text-center p-2">{{ __("تحميل المزيد...") }}</div>';
                }
            },
            matcher: function(params, data) {
                // إذا لم يكن هناك نص بحث، أظهر جميع الخيارات
                if ($.trim(params.term) === '') {
                    return data;
                }

                // البحث في النص والـ SKU
                const term = params.term.toLowerCase().trim();
                const text = data.text.toLowerCase();

                // البحث في اسم المنتج أو الـ SKU
                if (text.indexOf(term) > -1) {
                    return data;
                }

                // البحث في الـ SKU إذا كان موجود في النص
                const skuMatch = text.match(/\(([^)]+)\)/);
                if (skuMatch && skuMatch[1].toLowerCase().indexOf(term) > -1) {
                    return data;
                }

                return null;
            },
            templateResult: function(option) {
                if (!option.id) {
                    return option.text;
                }

                const $option = $(option.element);
                const price = $option.data('price') || '0';
                const tax = $option.data('tax') || '0';

                // تنسيق السعر
                const formattedPrice = parseFloat(price).toLocaleString('ar-SA', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });

                const stock = $option.data('stock') || '0';
                const stockColor = parseInt(stock) > 0 ? 'text-success' : 'text-danger';
                const stockIcon = parseInt(stock) > 0 ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';

                return $(`
                    <div class="product-option">
                        <div class="product-option-title">${option.text}</div>
                        <div class="product-option-details">
                            <div class="product-option-price">
                                <i class="fas fa-coins text-success me-1"></i>
                                <span>${formattedPrice} ر.س</span>
                            </div>
                            <div class="product-option-tax">
                                <i class="fas fa-percentage text-info me-1"></i>
                                <span>${tax}%</span>
                            </div>
                            <div class="product-option-stock">
                                <i class="${stockIcon} ${stockColor} me-1"></i>
                                <span class="${stockColor}">متاح: ${stock}</span>
                            </div>
                        </div>
                    </div>
                `);
            },
            templateSelection: function(option) {
                if (!option.id) {
                    return option.text;
                }

                // عرض اسم المنتج فقط في الحقل المحدد
                const text = option.text;
                const nameOnly = text.split(' (')[0]; // إزالة SKU من العرض
                return nameOnly;
            },
            escapeMarkup: function(markup) {
                return markup; // السماح بـ HTML في النتائج
            }
        });
    }

    function attachProductEvents(row) {
        const productSelect = row.querySelector('.product-select');
        const priceInput = row.querySelector('.price-input');
        const taxInput = row.querySelector('.tax-input');

        // تهيئة Select2 للمنتج
        initializeSelect2(productSelect);

        $(productSelect).on('select2:select', function(e) {
            const selectedOption = e.params.data.element;
            if (selectedOption) {
                priceInput.value = selectedOption.getAttribute('data-price') || 0;
                taxInput.value = selectedOption.getAttribute('data-tax') || '15.00';

                // تحديث الحد الأقصى للكمية
                const maxStock = selectedOption.getAttribute('data-stock') || 0;
                const quantityInput = row.querySelector('.quantity-input');
                quantityInput.setAttribute('max', maxStock);
                quantityInput.setAttribute('title', `الحد الأقصى: ${maxStock}`);

                calculateTotals();
            }
        });

        // إضافة تحقق من الكمية عند التغيير
        const quantityInput = row.querySelector('.quantity-input');
        quantityInput.addEventListener('input', function() {
            const selectedOption = productSelect.options[productSelect.selectedIndex];
            if (selectedOption && selectedOption.value) {
                const maxStock = parseInt(selectedOption.getAttribute('data-stock')) || 0;
                const currentQuantity = parseInt(this.value) || 0;

                if (currentQuantity > maxStock) {
                    this.style.borderColor = '#dc3545';
                    this.style.backgroundColor = '#f8d7da';
                    this.title = `تحذير: الكمية المطلوبة (${currentQuantity}) أكبر من المتاح (${maxStock})`;
                } else {
                    this.style.borderColor = '#ced4da';
                    this.style.backgroundColor = '#fff';
                    this.title = '';
                }
            }
            calculateTotals();
        });

        row.querySelectorAll('input:not(.quantity-input)').forEach(input => {
            input.addEventListener('input', calculateTotals);
        });
    }

    function calculateTotals() {
        let subtotal = 0;
        let totalDiscount = 0;
        let totalTax = 0;

        document.querySelectorAll('.product-row').forEach(row => {
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(row.querySelector('.price-input').value) || 0;
            const discount = parseFloat(row.querySelector('.discount-input').value) || 0;
            const tax = parseFloat(row.querySelector('.tax-input').value) || 0;

            const lineTotal = quantity * price;
            subtotal += lineTotal;
            totalDiscount += discount;
            totalTax += (lineTotal * tax / 100);
        });

        const total = subtotal - totalDiscount + totalTax;

        // تحديث العرض
        document.getElementById('subtotal').textContent = formatPrice(subtotal);
        document.getElementById('total-discount').textContent = formatPrice(totalDiscount);
        document.getElementById('total-tax').textContent = formatPrice(totalTax);
        document.getElementById('total').textContent = formatPrice(total);

        // تحديث مبالغ الدفع تلقائياً
        updatePaymentAmounts();
    }

    function formatPrice(amount) {
        // تنسيق السعر بالريال السعودي
        return amount.toLocaleString('ar-SA', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' ر.س';
    }

    // إدارة حقول الدفع
    function handlePaymentTypeChange() {
        const paymentType = document.getElementById('payment_type').value;
        const cashAmountGroup = document.getElementById('cash_amount_group');
        const networkAmountGroup = document.getElementById('network_amount_group');
        const transactionNumberGroup = document.getElementById('transaction_number_group');
        const cashAmountInput = document.getElementById('cash_amount');
        const networkAmountInput = document.getElementById('network_amount');

        // إخفاء جميع الحقول أولاً
        cashAmountGroup.style.display = 'none';
        networkAmountGroup.style.display = 'none';
        transactionNumberGroup.style.display = 'none';

        // عرض الحقول المناسبة حسب نوع الدفع
        if (paymentType === 'cash') {
            cashAmountGroup.style.display = 'block';
            // تعيين المبلغ النقدي إلى الإجمالي
            const totalElement = document.getElementById('total');
            if (totalElement) {
                const totalText = totalElement.textContent.replace(/[^\d.]/g, '');
                cashAmountInput.value = parseFloat(totalText) || 0;
            }
            networkAmountInput.value = 0;
        } else if (paymentType === 'network') {
            networkAmountGroup.style.display = 'block';
            transactionNumberGroup.style.display = 'block';
            // تعيين مبلغ الشبكة إلى الإجمالي
            const totalElement = document.getElementById('total');
            if (totalElement) {
                const totalText = totalElement.textContent.replace(/[^\d.]/g, '');
                networkAmountInput.value = parseFloat(totalText) || 0;
            }
            cashAmountInput.value = 0;
        } else if (paymentType === 'split') {
            cashAmountGroup.style.display = 'block';
            networkAmountGroup.style.display = 'block';
            transactionNumberGroup.style.display = 'block';
            // توزيع المبلغ بالتساوي
            const totalElement = document.getElementById('total');
            if (totalElement) {
                const totalText = totalElement.textContent.replace(/[^\d.]/g, '');
                const total = parseFloat(totalText) || 0;
                cashAmountInput.value = (total / 2).toFixed(2);
                networkAmountInput.value = (total / 2).toFixed(2);
            }
        }
    }

    // تحديث المبلغ الإجمالي عند تغيير المنتجات
    function updatePaymentAmounts() {
        const paymentType = document.getElementById('payment_type').value;
        const totalElement = document.getElementById('total');

        if (totalElement) {
            const totalText = totalElement.textContent.replace(/[^\d.]/g, '');
            const total = parseFloat(totalText) || 0;

            if (paymentType === 'cash') {
                document.getElementById('cash_amount').value = total.toFixed(2);
                document.getElementById('network_amount').value = '0.00';
            } else if (paymentType === 'network') {
                document.getElementById('network_amount').value = total.toFixed(2);
                document.getElementById('cash_amount').value = '0.00';
            } else if (paymentType === 'split') {
                document.getElementById('cash_amount').value = (total / 2).toFixed(2);
                document.getElementById('network_amount').value = (total / 2).toFixed(2);
            }
        }
    }

    // تطبيق event listeners على المنتجات الموجودة
    $(document).ready(function() {
        // تهيئة Select2 للمنتجات الموجودة
        document.querySelectorAll('.product-row').forEach(row => {
            attachProductEvents(row);
        });

        // تحديث عداد المنتجات وحالة أزرار الحذف
        updateProductsCount();

        // إضافة event listeners لأزرار الحذف الموجودة
        document.querySelectorAll('.remove-product').forEach(button => {
            button.addEventListener('click', function() {
                removeProduct(this);
            });
        });

        // إدارة تغيير نوع الدفع
        document.getElementById('payment_type').addEventListener('change', handlePaymentTypeChange);

        // تهيئة حقول الدفع عند تحميل الصفحة
        handlePaymentTypeChange();

        // التحقق من صحة النموذج قبل الإرسال
        document.getElementById('editProductsForm').addEventListener('submit', function(e) {
            const productRows = document.querySelectorAll('.product-row');
            if (productRows.length === 0) {
                e.preventDefault();
                alert('{{ __("خطأ: لا توجد منتجات في الفاتورة. يجب إضافة منتج واحد على الأقل قبل الحفظ.") }}');
                return false;
            }

            let hasValidProduct = false;
            productRows.forEach(row => {
                const productSelect = row.querySelector('.product-select');
                if (productSelect.value) {
                    hasValidProduct = true;
                }
            });

            if (!hasValidProduct) {
                e.preventDefault();
                alert('{{ __("يجب اختيار منتج واحد على الأقل") }}');
                return false;
            }

            // التحقق من صحة معلومات الدفع
            const paymentType = document.getElementById('payment_type').value;
            const cashAmount = parseFloat(document.getElementById('cash_amount').value) || 0;
            const networkAmount = parseFloat(document.getElementById('network_amount').value) || 0;
            const totalElement = document.getElementById('total');
            const totalText = totalElement.textContent.replace(/[^\d.]/g, '');
            const total = parseFloat(totalText) || 0;

            if (paymentType === 'split') {
                const paymentTotal = cashAmount + networkAmount;
                if (Math.abs(paymentTotal - total) > 0.01) {
                    e.preventDefault();
                    alert('{{ __("مجموع المبلغ النقدي ومبلغ الشبكة يجب أن يساوي الإجمالي") }}');
                    return false;
                }
            }
        });
    });
</script>
@endpush
