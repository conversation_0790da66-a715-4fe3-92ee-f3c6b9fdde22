{"__meta": {"id": "X9933fb7c02c24306070cf5c5943cbf3a", "datetime": "2025-06-19 12:34:55", "utime": **********.496111, "method": "GET", "uri": "/financial-operations/product-analytics/top-selling?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750336494.452625, "end": **********.496135, "duration": 1.0435099601745605, "duration_str": "1.04s", "measures": [{"label": "Booting", "start": 1750336494.452625, "relative_start": 0, "end": **********.310177, "relative_end": **********.310177, "duration": 0.8575520515441895, "duration_str": "858ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.310194, "relative_start": 0.8575689792633057, "end": **********.496137, "relative_end": 1.9073486328125e-06, "duration": 0.1859428882598877, "duration_str": "186ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47122760, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/top-selling", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getTopSellingProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.top-selling", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=206\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:206-364</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.048119999999999996, "accumulated_duration_str": "48.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.399561, "duration": 0.02913, "duration_str": "29.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 60.536}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4471102, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 60.536, "width_percent": 2.805}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 235}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.45451, "duration": 0.00487, "duration_str": "4.87ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:235", "source": "app/Http/Controllers/ProductAnalyticsController.php:235", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=235", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "235"}, "connection": "ty", "start_percent": 63.342, "width_percent": 10.121}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 240}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.463267, "duration": 0.00729, "duration_str": "7.29ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:240", "source": "app/Http/Controllers/ProductAnalyticsController.php:240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=240", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "240"}, "connection": "ty", "start_percent": 73.462, "width_percent": 15.15}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_v2_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos_v2` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 260}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.475018, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:260", "source": "app/Http/Controllers/ProductAnalyticsController.php:260", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=260", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "260"}, "connection": "ty", "start_percent": 88.612, "width_percent": 3.387}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, COALESCE(SUM(wp.quantity), 0) as stock_quantity from `product_services` as `ps` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` where `ps`.`id` in (3, 5, 7, 6) and `ps`.`created_by` = 15 group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name`, `psu`.`name`", "type": "query", "params": [], "bindings": ["3", "5", "7", "6", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 319}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.480413, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:319", "source": "app/Http/Controllers/ProductAnalyticsController.php:319", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=319", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "319"}, "connection": "ty", "start_percent": 91.999, "width_percent": 8.001}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/top-selling", "status_code": "<pre class=sf-dump id=sf-dump-1164372715 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1164372715\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2137146005 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137146005\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-494617691 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-494617691\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1934444040 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960; _clsk=1qi6yot%7C1750336490739%7C6%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhwTmdicHN6cmhUdnBCZlVweis1V3c9PSIsInZhbHVlIjoicnhVTktzR0Jpb21PZHNZaVV3RHZRMTJSa0VmcFFacEI0dlVKTUVBaHVWeE5vWEs2NlRaMEM0aHJodTlHNXFRbkp3WXNmTEEzcGNXcjVWaXZBMG1ycUdjSG5pUkVMSFpuQ1J2MHNTaXF4eXB3cWFheGo1bk1hbzJVbHl1MXlnQmtVd094SXQvUll2K1VqWHhrQnBvNi8wT3VpZDJCc3RWZGUwMnYzamt0SFR2N1hCc1VtZVBaZXNZSFFHZWZRam5xbGlYbmZXU2F0NnM0aXFKZ3dEZkhpSHZweGcwZTYrNVdBd0dwV0dJSjlvU3N2cFJGTWFtc204aHhMZCtoZGl3UXJXaDVFRVd3L016OXlaYjFaSXp5RnZNRVNTa215VXVBQndWOGNCNllEUTlqQzMyYWdJS0cvRWJ4KzVDRFJKWGd0eDVuTkV0S0dLaE0rT1M0V0dKVm41bkI1SHliNnd6NWxlNXhHNGltWTZHYkFTdEpYbFBNMFVpc2lVb2ExQWVWeDBlS0l3ekVZMzVuMEN1bGducXJTK0pWdDFCM3QxOGxIMWIyUTNSb0lGbHJ5aHlaSlA4RDlDM0xIT3hhMmtTM1drMEgveUFLZFJCYW5BdE8xT25Udk9OalYySnBTNUZEeGxsTzZZRUM2SGM3YTQ0clFxaEpuNDQ2am9nbE9WYWkiLCJtYWMiOiJiNDliZDA3MThlYTVjOWZhMjVjNzcyZDkzMjA0MDNhZDgzZGViNjEzNGFmMGM5NzQ3MzRmMTU5ZTFjODU5YmJiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImkxbHFNd3MwMnQ0dnFHSW9RRTVuMXc9PSIsInZhbHVlIjoiYTN2NUt5Q2lQb2NCS1VoVHAwdmtZakdCY0xudks4ZGNQa0U1REx2K25obFVUQmk3b0pmQm5RL1g4c1F2L1IxRGovTDhENm5SbUdySjU3dnFMdVBySzN3VTJ6ckxtK3dLUTVrcDZMZlZwS2JodytzQUVCKytpWVUxVGpIcE1FMjlvekpkSGZWbHdUTWdrVFV0c1ZCWHNsUVFLMnBCblhyeFZpVHhuTmNUUWtIMUhZVGRmT0JMa21mNUtnRUtUQUxLT2J3SFFxVHlIcDNMZWlXTlJmOWwrSDV6S2ZUQjcvSTJPUlhCUHhCUlMxNDd5Q3drdFZneFlydDN4U0hrR0VoRmhyWGw3WmxLMHhHdzFzR3JLaDZyRVQ5Rnh4S2RFWDA3MkxMZnd6OHY2OWsrMmFNMHdNYnBJSzZNK2Y1RGQzaEM0S2p4VWdVMEtPMEptWTlNaEtENktHNk5mTndhbm1VMUNDaWw3S2dQWjFMVW9jZ05PdVZ2b1Nmc0hwdkVqNlZzUnZsYzgrY3AxUUNLaUVkNzhhT3JKUEY0eCtSanc5UllOeFpnZ2dXZW1BZ0tvNzJGRU81bm8wN1VDeTFpVzAva3d3TjYxZHRYVjdKOW9sZDBNTWtzenBEa1dFeTYxNkFMSTVLMFQvd1orUU0xK1BWRTJjcGxCWUZ4QWxRZTM1K2MiLCJtYWMiOiI2MWQ1MmIzZmY2MDkzZDQ5ZGViZjUzM2U3OWZiODY2YzIyODA2ZTBiOTNlMjY2YWQ1NDcyMjMzYzQxZjIxNDg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1934444040\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1081026144 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BvzDAp8Ceepvt22TODQJA2h22xCzbnkTzOjAbsGW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081026144\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 12:34:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxGTnFPYnQvU1daRDF0L0M0WUV1WHc9PSIsInZhbHVlIjoiWElrejZxRXdERGFQOHYzditZNXo2eEc4MVNoOVFvWnhKcXloeDh2VVovT0d0SG14QjdRV2xJYTdHU0kyTzB1UCs3VExPdEVzYk52SmtBdm9rbzlZWVBZVU4wYTBOMXVlLzZuY3djSy9CQ0VGR2x4K0wzTFFlRVNObCthK3pJTk5MNkZlUEZ4eTU4bGxNeTJNYlpYT2JLRXhWNzZrS0F2d0Z3NEt3c3YzUm9XU0hRVHViV1c3WW1WTVF0VWwxYmE1N3Q0TldDVllYdlVQb2dFMjRIRU4wWG5uMGdWM3FncU1iS2dYckVOVDc0Rm5NeW00eGFENVpWdHRYWUx6eUJkRXpKTWFOUHVqbG84R2pwdnR3RWhhcHZQVjB3VXVPTy95anJTV2RyWTB3amdmYktqdTNuNk1zcUJWUlFwS3p2d09LZjYzSUw4NzBGNmg1c3RpREZ6Y3pVU3FnaWZwblVjVlFwZ2hETDZYVFdIWnhpUkUzS1M3dW1RY0ZBZUN5NjYxQzUyRVFSdFVVRm1PM1U4bWk3Rm4xOW0xbGw0U2dXZHBka0JEdGVhYzY2M2FEc2oxaW5uUWZqTGpBakdIaFRKV1FGa0RBZ3NyaFhFSXJkaG5VS29jaTk1ZXRkTGhYbUQ5Ym83ZEFyQWF3aGU2cnNhdkVkU2JzakgvL3BBbHhqSUQiLCJtYWMiOiJjNzM5ZjM4MWNkYzdmNGU5NTJmMWQzZTk4YjBhYjkxM2IwZTAwYjY4MWNlNWM3MmJhOTdkYmY0NWZhNWJhZjZjIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:34:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IldBcGhXTzNEd1N1aHNWcW5YSWU2aGc9PSIsInZhbHVlIjoiVVljbEFaQURvSEUvNnN4QnRpMlFSUjZ0ejBtSWZiTkRCMy9HQUxrb2gyVkhwR1htaWZVTk96VThhTVZVeFUrZklqblVyTnFTSUg4eWp3ZjdISGFlQkFFemUzNm9wSDM5VFJQSGRyc211WDFZUU5HZGRRdTV2cnlXNmR4bmZmQmk3TTh5Z0FyNEdJL3ZIbTZwbFVDY05IRkNIdFl4bmpJQ0YvWDBsaDJkeEFXZW16K0ErREl0NFFZNHlUL3pydFFsL29NbEk0Q3BJSXgrb2lhcHNtQnhsNU5QUHQxSlJCdmNhZG94cGlFWS91Rk9lbm12eEp2c0NYUFBDZkJmMlp2NnV4YzBKRnVTNjVzK3hmMXRnTW1UN3pGWHdzVlJiRXhsRFB1ZVBrMTI0TnRKa29tSTBnemRtaEJaVnlLNVBrSWtLWGU5ZEVJRHI5UUcrL2Mzenh4ZWNjUURodk83M2p3ZkdaVk93UEVMbWlxWGd4SGNYbUw4KzROKzJIR3dzMnY3YnVROG5Ha1JSSk56QlZQRVVkZ0ZVNy9GRVI3aDZNeG94dEk5cGNBVXZkOEl2UWIrNm1JNWtXY1lWclR3b1I3MEkwQlNLcVNWL0dFNFlCK1V1RFYrSlhDS3padFVCUkhOQjNCVmF4bWFpRU14NGtoVEZDRHpyOGFNZ3NKOEQzSUoiLCJtYWMiOiIwN2UyNWIwNDczYzg0MWEyNDRiNTkxNTMwZTI3YjcwNGJkNmJlMDM5ODQyNWJkODcxOWFiMmVmNzkxMjJhNTBhIiwidGFnIjoiIn0%3D; expires=Thu, 19 Jun 2025 14:34:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxGTnFPYnQvU1daRDF0L0M0WUV1WHc9PSIsInZhbHVlIjoiWElrejZxRXdERGFQOHYzditZNXo2eEc4MVNoOVFvWnhKcXloeDh2VVovT0d0SG14QjdRV2xJYTdHU0kyTzB1UCs3VExPdEVzYk52SmtBdm9rbzlZWVBZVU4wYTBOMXVlLzZuY3djSy9CQ0VGR2x4K0wzTFFlRVNObCthK3pJTk5MNkZlUEZ4eTU4bGxNeTJNYlpYT2JLRXhWNzZrS0F2d0Z3NEt3c3YzUm9XU0hRVHViV1c3WW1WTVF0VWwxYmE1N3Q0TldDVllYdlVQb2dFMjRIRU4wWG5uMGdWM3FncU1iS2dYckVOVDc0Rm5NeW00eGFENVpWdHRYWUx6eUJkRXpKTWFOUHVqbG84R2pwdnR3RWhhcHZQVjB3VXVPTy95anJTV2RyWTB3amdmYktqdTNuNk1zcUJWUlFwS3p2d09LZjYzSUw4NzBGNmg1c3RpREZ6Y3pVU3FnaWZwblVjVlFwZ2hETDZYVFdIWnhpUkUzS1M3dW1RY0ZBZUN5NjYxQzUyRVFSdFVVRm1PM1U4bWk3Rm4xOW0xbGw0U2dXZHBka0JEdGVhYzY2M2FEc2oxaW5uUWZqTGpBakdIaFRKV1FGa0RBZ3NyaFhFSXJkaG5VS29jaTk1ZXRkTGhYbUQ5Ym83ZEFyQWF3aGU2cnNhdkVkU2JzakgvL3BBbHhqSUQiLCJtYWMiOiJjNzM5ZjM4MWNkYzdmNGU5NTJmMWQzZTk4YjBhYjkxM2IwZTAwYjY4MWNlNWM3MmJhOTdkYmY0NWZhNWJhZjZjIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:34:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IldBcGhXTzNEd1N1aHNWcW5YSWU2aGc9PSIsInZhbHVlIjoiVVljbEFaQURvSEUvNnN4QnRpMlFSUjZ0ejBtSWZiTkRCMy9HQUxrb2gyVkhwR1htaWZVTk96VThhTVZVeFUrZklqblVyTnFTSUg4eWp3ZjdISGFlQkFFemUzNm9wSDM5VFJQSGRyc211WDFZUU5HZGRRdTV2cnlXNmR4bmZmQmk3TTh5Z0FyNEdJL3ZIbTZwbFVDY05IRkNIdFl4bmpJQ0YvWDBsaDJkeEFXZW16K0ErREl0NFFZNHlUL3pydFFsL29NbEk0Q3BJSXgrb2lhcHNtQnhsNU5QUHQxSlJCdmNhZG94cGlFWS91Rk9lbm12eEp2c0NYUFBDZkJmMlp2NnV4YzBKRnVTNjVzK3hmMXRnTW1UN3pGWHdzVlJiRXhsRFB1ZVBrMTI0TnRKa29tSTBnemRtaEJaVnlLNVBrSWtLWGU5ZEVJRHI5UUcrL2Mzenh4ZWNjUURodk83M2p3ZkdaVk93UEVMbWlxWGd4SGNYbUw4KzROKzJIR3dzMnY3YnVROG5Ha1JSSk56QlZQRVVkZ0ZVNy9GRVI3aDZNeG94dEk5cGNBVXZkOEl2UWIrNm1JNWtXY1lWclR3b1I3MEkwQlNLcVNWL0dFNFlCK1V1RFYrSlhDS3padFVCUkhOQjNCVmF4bWFpRU14NGtoVEZDRHpyOGFNZ3NKOEQzSUoiLCJtYWMiOiIwN2UyNWIwNDczYzg0MWEyNDRiNTkxNTMwZTI3YjcwNGJkNmJlMDM5ODQyNWJkODcxOWFiMmVmNzkxMjJhNTBhIiwidGFnIjoiIn0%3D; expires=Thu, 19-Jun-2025 14:34:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">glp5cBK57LUmKndHsTB7xlmY5nIjmgjbZq0ABQTM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}